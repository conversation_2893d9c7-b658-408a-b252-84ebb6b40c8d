#!/usr/bin/env python3
"""修复密码哈希"""

import sqlite3
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def fix_admin_password():
    """修复admin用户密码"""
    # 生成admin123的哈希
    admin_hash = pwd_context.hash("admin123")
    
    # 连接数据库
    conn = sqlite3.connect('work_log_system.db')
    cursor = conn.cursor()
    
    # 更新admin用户密码
    cursor.execute('UPDATE employee SET password = ? WHERE employee_name = ?', (admin_hash, 'admin'))
    conn.commit()
    
    print(f"已更新admin用户密码哈希: {admin_hash}")
    
    # 验证更新
    cursor.execute('SELECT employee_name, password FROM employee WHERE employee_name = ?', ('admin',))
    result = cursor.fetchone()
    if result:
        print(f"验证: 用户名={result[0]}, 密码哈希={result[1][:50]}...")
        # 验证密码
        is_valid = pwd_context.verify("admin123", result[1])
        print(f"密码验证结果: {is_valid}")
    
    conn.close()

if __name__ == "__main__":
    fix_admin_password()
