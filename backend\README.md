# 员工工时日志管理系统 - 后端

基于 FastAPI 的员工工时日志管理系统后端服务。

## 功能特性

- 员工认证和授权
- 工时日志管理
- 项目管理
- 部门管理
- 统计报表

## 技术栈

- FastAPI
- SQLAlchemy
- MySQL
- JWT 认证
- uv 包管理

## 快速开始

1. 安装依赖：
```bash
uv sync
```

2. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

3. 初始化数据库：
```bash
uv run python test_db.py
```

4. 启动服务：
```bash
uv run uvicorn app.main:app --reload
```

## API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 测试账号

- 张三 / 123456 (技术部)
- 李四 / 123456 (销售部经理)
- 王五 / 123456 (技术部)
- 赵六 / 123456 (销售部)
