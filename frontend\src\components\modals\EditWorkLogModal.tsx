'use client';

import { useState, useEffect, Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { workLogApi, projectApi } from '@/lib/api';
import { formatDate } from '@/lib/utils';
import type { Project, WorkLog } from '@/types';

const workLogSchema = z.object({
  work_date: z.string().min(1, "请选择工作日期"),
  project_number: z.string().min(1, "请选择项目"),
  work_hours: z.number().min(0.1, "工时必须大于0").max(8, "工时不能超过8小时"),
  work_content: z
    .string()
    .min(1, "请输入工作内容")
    .max(1000, "工作内容不能超过1000字符"),
  remarks: z.string().max(500, "备注不能超过500字符").optional(),
});

interface WorkLogFormData {
  work_date: string;
  project_number: string;
  work_hours: number;
  work_content: string;
  remarks?: string;
}

interface EditWorkLogModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  workLog: WorkLog | null;
}

export default function EditWorkLogModal({ isOpen, onClose, onSuccess, workLog }: EditWorkLogModalProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<WorkLogFormData>({
    resolver: zodResolver(workLogSchema),
  });

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true);
        const projectsData = await projectApi.getAll();
        setProjects(projectsData);
      } catch (error) {
        console.error('获取项目列表失败:', error);
        toast.error('获取项目列表失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchProjects();
    }
  }, [isOpen]);

  useEffect(() => {
    if (workLog && isOpen) {
      setValue('work_date', workLog.work_date);
      setValue('project_number', workLog.project_number);
      setValue('work_hours', parseFloat(workLog.work_hours.toString()));
      setValue('work_content', workLog.work_content);
      setValue('remarks', workLog.remarks || '');
    }
  }, [workLog, isOpen, setValue]);

  const onSubmit = async (data: WorkLogFormData) => {
    if (!workLog) return;

    try {
      await workLogApi.update(workLog.id, data);
      toast.success('工作日志更新成功');
      reset();
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('更新工作日志失败:', error);
      const message = error.response?.data?.detail || '更新工作日志失败';
      toast.error(message);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <ClipboardDocumentListIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        编辑工作日志
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">修改工作日志信息</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        工作日期 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        {...register("work_date")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.work_date ? "border-red-300" : "border-gray-300"
                        }`}
                      />
                      {errors.work_date && (
                        <p className="mt-1 text-sm text-red-600">{errors.work_date.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        工作时长（小时） <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        step="0.5"
                        min="0"
                        max="8"
                        {...register("work_hours", { valueAsNumber: true })}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.work_hours ? "border-red-300" : "border-gray-300"
                        }`}
                        placeholder="请输入有效值（0-8）"
                      />
                      {errors.work_hours && (
                        <p className="mt-1 text-sm text-red-600">{errors.work_hours.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      项目 <span className="text-red-500">*</span>
                    </label>
                    <select
                      {...register("project_number")}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.project_number ? "border-red-300" : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">请选择项目</option>
                      {projects.map((project) => (
                        <option key={project.project_number} value={project.project_number}>
                          {project.project_name} ({project.customer_name})
                        </option>
                      ))}
                    </select>
                    {errors.project_number && (
                      <p className="mt-1 text-sm text-red-600">{errors.project_number.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      工作内容 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      {...register("work_content")}
                      rows={4}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.work_content ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="请详细描述今天的工作内容..."
                    />
                    {errors.work_content && (
                      <p className="mt-1 text-sm text-red-600">{errors.work_content.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">备注</label>
                    <textarea
                      {...register("remarks")}
                      rows={2}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.remarks ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="可选的备注信息..."
                    />
                    {errors.remarks && (
                      <p className="mt-1 text-sm text-red-600">{errors.remarks.message}</p>
                    )}
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || isLoading}
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          更新中...
                        </div>
                      ) : (
                        '更新日志'
                      )}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
