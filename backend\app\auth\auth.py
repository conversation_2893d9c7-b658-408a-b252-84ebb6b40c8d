"""认证功能实现"""
from datetime import datetime, timedelta
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session

from ..config import settings
from ..database import get_db
from ..models.employee import Employee
from ..schemas.auth import TokenData

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer 认证
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """验证令牌"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        employee_name: str = payload.get("sub")
        if employee_name is None:
            raise credentials_exception
        token_data = TokenData(employee_name=employee_name)
    except JWTError:
        raise credentials_exception
    
    return token_data


def get_current_employee(
    token_data: TokenData = Depends(verify_token),
    db: Session = Depends(get_db)
) -> Employee:
    """获取当前员工"""
    employee = db.query(Employee).filter(Employee.employee_name == token_data.employee_name).first()
    if employee is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return employee


def authenticate_employee(db: Session, employee_name: str, password: str) -> Optional[Employee]:
    """认证员工"""
    employee = db.query(Employee).filter(
        Employee.employee_name == employee_name,
        Employee.delete_flag == False
    ).first()
    if not employee:
        return None
    if not verify_password(password, employee.password):
        return None
    return employee
