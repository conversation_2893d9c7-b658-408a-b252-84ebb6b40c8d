{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/CreateWorkLogModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport {\n  XMarkIcon,\n  ClockIcon,\n  DocumentTextIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLogApi, projectApi } from \"@/lib/api\";\nimport { getTodayString } from \"@/lib/utils\";\nimport type { Project, WorkLogFormData } from \"@/types\";\n\nconst workLogSchema = z.object({\n  work_date: z.string().min(1, \"请选择工作日期\"),\n  project_number: z.string().min(1, \"请选择项目\"),\n  work_hours: z.number().min(0.1, \"工时必须大于0\").max(8, \"工时不能超过8小时\"),\n  work_content: z\n    .string()\n    .min(1, \"请输入工作内容\")\n    .max(1000, \"工作内容不能超过1000字符\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface CreateWorkLogModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function CreateWorkLogModal({\n  isOpen,\n  onClose,\n  onSuccess,\n}: CreateWorkLogModalProps) {\n  const { user } = useAuth();\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch,\n  } = useForm<WorkLogFormData>({\n    resolver: zodResolver(workLogSchema),\n    defaultValues: {\n      work_date: getTodayString(),\n      project_number: \"\",\n      work_hours: 8,\n      work_content: \"\",\n      remarks: \"\",\n    },\n  });\n\n  const selectedProjectNumber = watch(\"project_number\");\n  const selectedProject = projects.find(\n    (p) => p.project_number === selectedProjectNumber\n  );\n\n  useEffect(() => {\n    const fetchProjects = async () => {\n      try {\n        setIsLoading(true);\n        const projectsData = await projectApi.getAll({ limit: 100 });\n        setProjects(projectsData);\n      } catch (error) {\n        console.error(\"获取项目列表失败:\", error);\n        toast.error(\"获取项目列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchProjects();\n    }\n  }, [isOpen]);\n\n  const onSubmit = async (data: WorkLogFormData) => {\n    if (!user) return;\n\n    try {\n      setIsSubmitting(true);\n      await workLogApi.create({\n        ...data,\n        employee_id: user.id,\n        work_hours: Number(data.work_hours),\n      });\n\n      toast.success(\"工作日志创建成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"创建工作日志失败:\", error);\n      const message = error.response?.data?.detail || \"创建工作日志失败\";\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        新建工作日志\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">\n                        记录您的工作内容和时间\n                      </p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 工作日期 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作日期 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"date\"\n                        {...register(\"work_date\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.work_date\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                      />\n                      {errors.work_date && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.work_date.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 工作时长 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作时长（小时） <span className=\"text-red-500\">*</span>\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                          <ClockIcon className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"number\"\n                          step=\"0.5\"\n                          min=\"0.1\"\n                          max=\"8\"\n                          {...register(\"work_hours\", { valueAsNumber: true })}\n                          className={`block w-full pl-10 pr-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                            errors.work_hours\n                              ? \"border-red-300\"\n                              : \"border-gray-300\"\n                          }`}\n                          placeholder=\"8.0\"\n                        />\n                      </div>\n                      {errors.work_hours && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.work_hours.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 项目选择 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"project_number\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_number\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value=\"\">请选择项目</option>\n                      {projects.map((project) => (\n                        <option\n                          key={project.project_number}\n                          value={project.project_number}\n                        >\n                          {project.project_name} ({project.project_number})\n                        </option>\n                      ))}\n                    </select>\n                    {errors.project_number && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.project_number.message}\n                      </p>\n                    )}\n                    {selectedProject && (\n                      <div className=\"mt-2 p-3 bg-blue-50 rounded-lg\">\n                        <div className=\"flex items-center space-x-2 text-sm\">\n                          <span className=\"font-medium text-blue-900\">\n                            客户:\n                          </span>\n                          <span className=\"text-blue-700\">\n                            {selectedProject.customer_name}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm mt-1\">\n                          <span className=\"font-medium text-blue-900\">\n                            类型:\n                          </span>\n                          <span className=\"text-blue-700\">\n                            {selectedProject.project_type}\n                          </span>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* 工作内容 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      工作内容 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <textarea\n                      {...register(\"work_content\")}\n                      rows={4}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.work_content\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      placeholder=\"请详细描述您今天的工作内容...\"\n                    />\n                    {errors.work_content && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.work_content.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={2}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          创建中...\n                        </div>\n                      ) : (\n                        \"创建日志\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;AAkBA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG;IAClD,cAAc,gLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,MAAM;IACb,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAQe,SAAS,mBAAmB,KAIjB;QAJiB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACe,GAJiB;;IAKzC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;YACxB,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,SAAS;QACX;IACF;IAEA,MAAM,wBAAwB,MAAM;IACpC,MAAM,kBAAkB,SAAS,IAAI,CACnC,CAAC,IAAM,EAAE,cAAc,KAAK;IAG9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;8DAAgB;oBACpB,IAAI;wBACF,aAAa;wBACb,MAAM,eAAe,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;4BAAE,OAAO;wBAAI;wBAC1D,YAAY;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,QAAQ;gBACV;YACF;QACF;uCAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,gBAAgB;YAChB,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,EAAE;gBACpB,YAAY,OAAO,KAAK,UAAU;YACpC;YAEA,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;gBAEH,sBAAA;YADhB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;YAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBAC3C,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;;;;;;kEAE9B,6LAAC;;0EACC,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAKzC,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,YAAY;gEACzB,WAAW,AAAC,2IAIX,OAHC,OAAO,SAAS,GACZ,mBACA;;;;;;4DAGP,OAAO,SAAS,kBACf,6LAAC;gEAAE,WAAU;0EACV,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kEAM/B,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACrD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAE1C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;kFAEvB,6LAAC;wEACC,MAAK;wEACL,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACH,GAAG,SAAS,cAAc;4EAAE,eAAe;wEAAK,EAAE;wEACnD,WAAW,AAAC,iJAIX,OAHC,OAAO,UAAU,GACb,mBACA;wEAEN,aAAY;;;;;;;;;;;;4DAGf,OAAO,UAAU,kBAChB,6LAAC;gEAAE,WAAU;0EACV,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAOlC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EAC3D,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEpC,6LAAC;wDACE,GAAG,SAAS,iBAAiB;wDAC9B,WAAW,AAAC,2IAIX,OAHC,OAAO,cAAc,GACjB,mBACA;wDAEN,UAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oEAEC,OAAO,QAAQ,cAAc;;wEAE5B,QAAQ,YAAY;wEAAC;wEAAG,QAAQ,cAAc;wEAAC;;mEAH3C,QAAQ,cAAc;;;;;;;;;;;oDAOhC,OAAO,cAAc,kBACpB,6LAAC;wDAAE,WAAU;kEACV,OAAO,cAAc,CAAC,OAAO;;;;;;oDAGjC,iCACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAG5C,6LAAC;wEAAK,WAAU;kFACb,gBAAgB,aAAa;;;;;;;;;;;;0EAGlC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAG5C,6LAAC;wEAAK,WAAU;kFACb,gBAAgB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAQvC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EACzD,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,6LAAC;wDACE,GAAG,SAAS,eAAe;wDAC5B,MAAM;wDACN,WAAW,AAAC,2IAIX,OAHC,OAAO,YAAY,GACf,mBACA;wDAEN,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,6LAAC;wDAAE,WAAU;kEACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAMlC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,AAAC,2IAEX,OADC,OAAO,OAAO,GAAG,mBAAmB;wDAEtC,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,6LAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,6LAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB;GA9UwB;;QAKL,0HAAA,CAAA,UAAO;QAWpB,iKAAA,CAAA,UAAO;;;KAhBW", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  ClipboardDocumentListIcon,\n  FolderIcon,\n  UserGroupIcon,\n  ClockIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLog<PERSON>pi, projectApi, employeeApi } from \"@/lib/api\";\nimport { formatHours, formatDate, getTodayString } from \"@/lib/utils\";\nimport CreateWorkLogModal from \"@/components/modals/CreateWorkLogModal\";\nimport type { WorkLogStatistics, WorkLog } from \"@/types\";\n\ninterface DashboardStats {\n  totalHours: number;\n  totalLogs: number;\n  totalProjects: number;\n  totalEmployees: number;\n}\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalHours: 0,\n    totalLogs: 0,\n    totalProjects: 0,\n    totalEmployees: 0,\n  });\n  const [recentLogs, setRecentLogs] = useState<WorkLog[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setIsLoading(true);\n\n        // 获取工时统计\n        const workLogStats = await workLogApi.getStatistics({\n          start_date: new Date(\n            new Date().getFullYear(),\n            new Date().getMonth(),\n            1\n          )\n            .toISOString()\n            .split(\"T\")[0],\n          end_date: getTodayString(),\n        });\n\n        // 获取最近的工作日志（管理员可以查看所有，普通用户只能查看自己的）\n        const logs = user?.is_admin\n          ? await workLogApi.getAll({ limit: 10 })\n          : await workLogApi.getMy({ limit: 5 });\n\n        // 如果是销售人员，获取更多统计信息\n        let projectCount = 0;\n        let employeeCount = 0;\n\n        if (user?.is_sales) {\n          const [projects, employees] = await Promise.all([\n            projectApi.getAll({ limit: 1000 }),\n            employeeApi.getAll({ limit: 1000 }),\n          ]);\n          projectCount = projects.length;\n          employeeCount = employees.length;\n        }\n\n        setStats({\n          totalHours: workLogStats.total_hours,\n          totalLogs: workLogStats.total_logs,\n          totalProjects: projectCount,\n          totalEmployees: employeeCount,\n        });\n        setRecentLogs(logs);\n      } catch (error) {\n        console.error(\"获取仪表板数据失败:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (user) {\n      fetchDashboardData();\n    }\n  }, [user]);\n\n  const statCards = [\n    {\n      name: \"本月工时\",\n      value: formatHours(stats.totalHours),\n      icon: ClockIcon,\n      gradient: \"from-blue-500 to-blue-600\",\n      bgColor: \"bg-blue-50\",\n      iconColor: \"text-blue-600\",\n      change: \"+12%\",\n      changeType: \"increase\",\n    },\n    {\n      name: \"工作日志\",\n      value: stats.totalLogs.toString(),\n      icon: ClipboardDocumentListIcon,\n      gradient: \"from-green-500 to-green-600\",\n      bgColor: \"bg-green-50\",\n      iconColor: \"text-green-600\",\n      change: \"+8%\",\n      changeType: \"increase\",\n    },\n    ...(user?.is_sales\n      ? [\n          {\n            name: \"项目数量\",\n            value: stats.totalProjects.toString(),\n            icon: FolderIcon,\n            gradient: \"from-yellow-500 to-yellow-600\",\n            bgColor: \"bg-yellow-50\",\n            iconColor: \"text-yellow-600\",\n            change: \"+3%\",\n            changeType: \"increase\",\n          },\n          {\n            name: \"员工数量\",\n            value: stats.totalEmployees.toString(),\n            icon: UserGroupIcon,\n            gradient: \"from-purple-500 to-purple-600\",\n            bgColor: \"bg-purple-50\",\n            iconColor: \"text-purple-600\",\n            change: \"+2%\",\n            changeType: \"increase\",\n          },\n        ]\n      : []),\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"h-8 w-8 bg-gray-300 rounded\"></div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <div className=\"h-4 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 欢迎信息 */}\n      <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold\">\n              欢迎回来，{user?.employee_name}！\n            </h1>\n            <p className=\"mt-2 text-blue-100\">\n              今天是 {formatDate(new Date(), \"yyyy年MM月dd日\")}，祝您工作愉快！\n            </p>\n            <div className=\"mt-4 flex items-center space-x-4\">\n              <div className=\"flex items-center\">\n                <div className=\"h-3 w-3 bg-green-400 rounded-full mr-2\"></div>\n                <span className=\"text-sm\">系统运行正常</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"h-3 w-3 bg-yellow-400 rounded-full mr-2\"></div>\n                <span className=\"text-sm\">\n                  {user?.department?.department_name}\n                </span>\n              </div>\n            </div>\n          </div>\n          <div className=\"hidden md:block\">\n            <div className=\"h-24 w-24 bg-white/20 rounded-full flex items-center justify-center\">\n              <svg\n                className=\"h-12 w-12 text-white\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {statCards.map((item) => (\n          <div\n            key={item.name}\n            className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center\">\n                    <div className={`p-3 rounded-xl ${item.bgColor}`}>\n                      <item.icon className={`h-6 w-6 ${item.iconColor}`} />\n                    </div>\n                    <div className=\"ml-4\">\n                      <p className=\"text-sm font-medium text-gray-600 truncate\">\n                        {item.name}\n                      </p>\n                      <p className=\"text-2xl font-bold text-gray-900\">\n                        {item.value}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <span\n                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                        item.changeType === \"increase\"\n                          ? \"bg-green-100 text-green-800\"\n                          : \"bg-red-100 text-red-800\"\n                      }`}\n                    >\n                      {item.changeType === \"increase\" ? (\n                        <svg\n                          className=\"w-3 h-3 mr-1\"\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path\n                            fillRule=\"evenodd\"\n                            d=\"M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z\"\n                            clipRule=\"evenodd\"\n                          />\n                        </svg>\n                      ) : (\n                        <svg\n                          className=\"w-3 h-3 mr-1\"\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path\n                            fillRule=\"evenodd\"\n                            d=\"M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z\"\n                            clipRule=\"evenodd\"\n                          />\n                        </svg>\n                      )}\n                      {item.change}\n                    </span>\n                    <span className=\"ml-2 text-xs text-gray-500\">vs 上月</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* 最近的工作日志 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100\">\n        <div className=\"px-6 py-5 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-xl font-semibold text-gray-900\">\n              最近的工作日志\n            </h3>\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              最新 {recentLogs.length} 条\n            </span>\n          </div>\n        </div>\n        <div className=\"p-6\">\n          {recentLogs.length > 0 ? (\n            <div className=\"space-y-4\">\n              {recentLogs.map((log) => (\n                <div\n                  key={log.id}\n                  className=\"bg-white border border-gray-200 rounded-xl p-5 hover:shadow-md transition-all duration-200\"\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\">\n                        <ClipboardDocumentListIcon className=\"h-6 w-6 text-white\" />\n                      </div>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <h4 className=\"text-sm font-semibold text-gray-900 truncate\">\n                              {log.project?.project_name || \"未知项目\"}\n                            </h4>\n                            <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                              {formatHours(log.work_hours)}\n                            </span>\n                          </div>\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <span className=\"text-xs text-gray-500\">客户:</span>\n                            <span className=\"text-xs font-medium text-gray-700 truncate\">\n                              {log.project?.customer_name || \"未知客户\"}\n                            </span>\n                            {user?.is_admin && (\n                              <>\n                                <span className=\"text-xs text-gray-400\">•</span>\n                                <span className=\"text-xs text-gray-500\">\n                                  员工:\n                                </span>\n                                <span className=\"text-xs font-medium text-gray-700\">\n                                  {log.employee?.employee_name || \"未知员工\"}\n                                </span>\n                              </>\n                            )}\n                          </div>\n                          <p className=\"text-sm text-gray-700 line-clamp-2 mb-2\">\n                            {log.work_content}\n                          </p>\n                          {log.remarks && (\n                            <p className=\"text-xs text-gray-500 italic\">\n                              备注: {log.remarks}\n                            </p>\n                          )}\n                        </div>\n                        <div className=\"flex-shrink-0 ml-4\">\n                          <div className=\"text-right\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {formatDate(log.work_date)}\n                            </div>\n                            <div className=\"text-xs text-gray-500\">\n                              {new Date(log.created_at).toLocaleTimeString(\n                                \"zh-CN\",\n                                {\n                                  hour: \"2-digit\",\n                                  minute: \"2-digit\",\n                                }\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <div className=\"text-center pt-4\">\n                <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\">\n                  查看全部日志\n                  <svg\n                    className=\"ml-2 h-4 w-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M9 5l7 7-7 7\"\n                    />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center\">\n                <ClipboardDocumentListIcon className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                暂无工作日志\n              </h3>\n              <p className=\"mt-2 text-sm text-gray-500\">\n                开始记录您的第一条工作日志吧！\n              </p>\n              <div className=\"mt-6\">\n                <button\n                  onClick={() => setShowCreateModal(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200\"\n                >\n                  <svg\n                    className=\"mr-2 h-4 w-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M12 4v16m8-8H4\"\n                    />\n                  </svg>\n                  创建工作日志\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 新建工作日志模态框 */}\n      <CreateWorkLogModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={() => {\n          // 刷新数据\n          window.location.reload();\n        }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AACA;;;AAZA;;;;;;;AAsBe,SAAS;QA6JL;;IA5JjB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,WAAW;QACX,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;8DAAqB;oBACzB,IAAI;wBACF,aAAa;wBAEb,SAAS;wBACT,MAAM,eAAe,MAAM,oHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;4BAClD,YAAY,IAAI,KACd,IAAI,OAAO,WAAW,IACtB,IAAI,OAAO,QAAQ,IACnB,GAEC,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE;4BAChB,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;wBACzB;wBAEA,mCAAmC;wBACnC,MAAM,OAAO,CAAA,iBAAA,2BAAA,KAAM,QAAQ,IACvB,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;4BAAE,OAAO;wBAAG,KACpC,MAAM,oHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;4BAAE,OAAO;wBAAE;wBAEtC,mBAAmB;wBACnB,IAAI,eAAe;wBACnB,IAAI,gBAAgB;wBAEpB,IAAI,iBAAA,2BAAA,KAAM,QAAQ,EAAE;4BAClB,MAAM,CAAC,UAAU,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC9C,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oCAAE,OAAO;gCAAK;gCAChC,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oCAAE,OAAO;gCAAK;6BAClC;4BACD,eAAe,SAAS,MAAM;4BAC9B,gBAAgB,UAAU,MAAM;wBAClC;wBAEA,SAAS;4BACP,YAAY,aAAa,WAAW;4BACpC,WAAW,aAAa,UAAU;4BAClC,eAAe;4BACf,gBAAgB;wBAClB;wBACA,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,cAAc;oBAC9B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,UAAU;YACnC,MAAM,oNAAA,CAAA,YAAS;YACf,UAAU;YACV,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,MAAM,SAAS,CAAC,QAAQ;YAC/B,MAAM,oPAAA,CAAA,4BAAyB;YAC/B,UAAU;YACV,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;WACI,CAAA,iBAAA,2BAAA,KAAM,QAAQ,IACd;YACE;gBACE,MAAM;gBACN,OAAO,MAAM,aAAa,CAAC,QAAQ;gBACnC,MAAM,sNAAA,CAAA,aAAU;gBAChB,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,YAAY;YACd;YACA;gBACE,MAAM;gBACN,OAAO,MAAM,cAAc,CAAC,QAAQ;gBACpC,MAAM,4NAAA,CAAA,gBAAa;gBACnB,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,YAAY;YACd;SACD,GACD,EAAE;KACP;IAED,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBARb;;;;;;;;;;;;;;;IAiBpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAqB;wCAC3B,iBAAA,2BAAA,KAAM,aAAa;wCAAC;;;;;;;8CAE5B,6LAAC;oCAAE,WAAU;;wCAAqB;wCAC3B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,QAAQ;wCAAe;;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,kBAA8B,OAAb,KAAK,OAAO;8DAC5C,cAAA,6LAAC,KAAK,IAAI;wDAAC,WAAW,AAAC,WAAyB,OAAf,KAAK,SAAS;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAW,AAAC,2EAIX,OAHC,KAAK,UAAU,KAAK,aAChB,gCACA;;wDAGL,KAAK,UAAU,KAAK,2BACnB,6LAAC;4DACC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEACC,UAAS;gEACT,GAAE;gEACF,UAAS;;;;;;;;;;iFAIb,6LAAC;4DACC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEACC,UAAS;gEACT,GAAE;gEACF,UAAS;;;;;;;;;;;wDAId,KAAK,MAAM;;;;;;;8DAEd,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtDhD,KAAK,IAAI;;;;;;;;;;0BAgEpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAK,WAAU;;wCAAgG;wCAC1G,WAAW,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAI5B,6LAAC;wBAAI,WAAU;kCACZ,WAAW,MAAM,GAAG,kBACnB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC;wCAgBA,cASA,eASI;yDAjCnB,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FACX,EAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,YAAY,KAAI;;;;;;0FAEhC,6LAAC;gFAAK,WAAU;0FACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;kFAG/B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAwB;;;;;;0FACxC,6LAAC;gFAAK,WAAU;0FACb,EAAA,gBAAA,IAAI,OAAO,cAAX,oCAAA,cAAa,aAAa,KAAI;;;;;;4EAEhC,CAAA,iBAAA,2BAAA,KAAM,QAAQ,mBACb;;kGACE,6LAAC;wFAAK,WAAU;kGAAwB;;;;;;kGACxC,6LAAC;wFAAK,WAAU;kGAAwB;;;;;;kGAGxC,6LAAC;wFAAK,WAAU;kGACb,EAAA,gBAAA,IAAI,QAAQ,cAAZ,oCAAA,cAAc,aAAa,KAAI;;;;;;;;;;;;;;kFAKxC,6LAAC;wEAAE,WAAU;kFACV,IAAI,YAAY;;;;;;oEAElB,IAAI,OAAO,kBACV,6LAAC;wEAAE,WAAU;;4EAA+B;4EACrC,IAAI,OAAO;;;;;;;;;;;;;0EAItB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;sFAE3B,6LAAC;4EAAI,WAAU;sFACZ,IAAI,KAAK,IAAI,UAAU,EAAE,kBAAkB,CAC1C,SACA;gFACE,MAAM;gFACN,QAAQ;4EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzDT,IAAI,EAAE;;;;;;8CAmEf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;;4CAAoP;0DAEpQ,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;wCAAC,WAAU;;;;;;;;;;;8CAEvC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;4CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,6LAAC,qJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,WAAW;oBACT,OAAO;oBACP,OAAO,QAAQ,CAAC,MAAM;gBACxB;;;;;;;;;;;;AAIR;GAhZwB;;QACL,0HAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}