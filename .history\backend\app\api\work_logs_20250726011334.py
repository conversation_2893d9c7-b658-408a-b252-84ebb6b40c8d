"""工作日志管理 API"""
from datetime import date
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func

from ..auth import get_current_employee
from ..database import get_db
from ..models.employee import Employee
from ..models.project import Project
from ..models.work_log import WorkLog
from ..schemas.work_log import WorkLog as WorkLogSchema, WorkLogCreate, WorkLogUpdate

router = APIRouter(prefix="/work-logs", tags=["工作日志"])


@router.get("/", response_model=List[WorkLogSchema], summary="获取工作日志列表")
async def get_work_logs(
    skip: int = 0,
    limit: int = 100,
    employee_id: Optional[int] = Query(None, description="员工ID"),
    project_number: Optional[str] = Query(None, description="项目号"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取工作日志列表"""
    query = db.query(WorkLog).options(
        joinedload(WorkLog.employee),
        joinedload(WorkLog.project)
    )
    
    # 如果不是管理员，只能查看自己的日志
    if not current_employee.is_sales:  # 假设销售人员有更高权限
        query = query.filter(WorkLog.employee_id == current_employee.id)
    elif employee_id:
        query = query.filter(WorkLog.employee_id == employee_id)
    
    if project_number:
        query = query.filter(WorkLog.project_number == project_number)
    
    if start_date:
        query = query.filter(WorkLog.work_date >= start_date)
    
    if end_date:
        query = query.filter(WorkLog.work_date <= end_date)
    
    work_logs = query.order_by(WorkLog.work_date.desc()).offset(skip).limit(limit).all()
    return work_logs


@router.get("/my", response_model=List[WorkLogSchema], summary="获取我的工作日志")
async def get_my_work_logs(
    skip: int = 0,
    limit: int = 100,
    project_number: Optional[str] = Query(None, description="项目号"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取当前员工的工作日志"""
    query = db.query(WorkLog).options(
        joinedload(WorkLog.employee),
        joinedload(WorkLog.project)
    ).filter(WorkLog.employee_id == current_employee.id)
    
    if project_number:
        query = query.filter(WorkLog.project_number == project_number)
    
    if start_date:
        query = query.filter(WorkLog.work_date >= start_date)
    
    if end_date:
        query = query.filter(WorkLog.work_date <= end_date)
    
    work_logs = query.order_by(WorkLog.work_date.desc()).offset(skip).limit(limit).all()
    return work_logs


@router.get("/statistics", summary="获取工时统计")
async def get_work_log_statistics(
    employee_id: Optional[int] = Query(None, description="员工ID"),
    project_number: Optional[str] = Query(None, description="项目号"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取工时统计信息"""
    query = db.query(
        func.sum(WorkLog.work_hours).label("total_hours"),
        func.count(WorkLog.id).label("total_logs"),
        func.avg(WorkLog.work_hours).label("avg_hours")
    )
    
    # 权限控制
    if not current_employee.is_sales:
        query = query.filter(WorkLog.employee_id == current_employee.id)
    elif employee_id:
        query = query.filter(WorkLog.employee_id == employee_id)
    
    if project_number:
        query = query.filter(WorkLog.project_number == project_number)
    
    if start_date:
        query = query.filter(WorkLog.work_date >= start_date)
    
    if end_date:
        query = query.filter(WorkLog.work_date <= end_date)
    
    result = query.first()
    
    return {
        "total_hours": float(result.total_hours or 0),
        "total_logs": result.total_logs or 0,
        "avg_hours": float(result.avg_hours or 0)
    }


@router.get("/{work_log_id}", response_model=WorkLogSchema, summary="获取工作日志详情")
async def get_work_log(
    work_log_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取工作日志详情"""
    work_log = db.query(WorkLog).options(
        joinedload(WorkLog.employee),
        joinedload(WorkLog.project)
    ).filter(WorkLog.id == work_log_id).first()
    
    if work_log is None:
        raise HTTPException(status_code=404, detail="工作日志不存在")
    
    # 权限检查：只能查看自己的日志或销售人员可以查看所有
    if not current_employee.is_sales and work_log.employee_id != current_employee.id:
        raise HTTPException(status_code=403, detail="没有权限查看此日志")
    
    return work_log


@router.post("/", response_model=WorkLogSchema, summary="创建工作日志")
async def create_work_log(
    work_log: WorkLogCreate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """创建工作日志"""
    # 检查项目是否存在
    project = db.query(Project).filter(Project.project_number == work_log.project_number).first()
    if not project:
        raise HTTPException(status_code=400, detail="项目不存在")
    
    # 检查是否已存在相同日期、员工、项目的日志
    existing_log = db.query(WorkLog).filter(
        and_(
            WorkLog.work_date == work_log.work_date,
            WorkLog.employee_id == work_log.employee_id,
            WorkLog.project_number == work_log.project_number
        )
    ).first()
    
    if existing_log:
        raise HTTPException(status_code=400, detail="该日期该项目的工作日志已存在")
    
    # 只能为自己创建日志（除非是销售人员）
    if not current_employee.is_sales and work_log.employee_id != current_employee.id:
        raise HTTPException(status_code=403, detail="只能为自己创建工作日志")
    
    db_work_log = WorkLog(**work_log.model_dump())
    db.add(db_work_log)
    db.commit()
    db.refresh(db_work_log)
    
    # 重新查询以包含关联数据
    work_log_with_relations = db.query(WorkLog).options(
        joinedload(WorkLog.employee),
        joinedload(WorkLog.project)
    ).filter(WorkLog.id == db_work_log.id).first()
    
    return work_log_with_relations
