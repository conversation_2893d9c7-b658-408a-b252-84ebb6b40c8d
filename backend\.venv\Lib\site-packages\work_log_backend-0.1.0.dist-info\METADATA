Metadata-Version: 2.4
Name: work-log-backend
Version: 0.1.0
Summary: 员工工时日志管理系统后端API
Author-email: Developer <<EMAIL>>
Requires-Python: >=3.11
Requires-Dist: alembic>=1.13.0
Requires-Dist: cryptography>=41.0.7
Requires-Dist: fastapi>=0.104.1
Requires-Dist: passlib[bcrypt]>=1.7.4
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pymysql>=1.1.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-jose[cryptography]>=3.3.0
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: requests>=2.32.4
Requires-Dist: sqlalchemy>=2.0.23
Requires-Dist: uvicorn[standard]>=0.24.0
Description-Content-Type: text/markdown

# 员工工时日志管理系统 - 后端

基于 FastAPI 的员工工时日志管理系统后端服务。

## 功能特性

- 员工认证和授权
- 工时日志管理
- 项目管理
- 部门管理
- 统计报表

## 技术栈

- FastAPI
- SQLAlchemy
- MySQL
- JWT 认证
- uv 包管理

## 快速开始

1. 安装依赖：
```bash
uv sync
```

2. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

3. 初始化数据库：
```bash
uv run python test_db.py
```

4. 启动服务：
```bash
uv run uvicorn app.main:app --reload
```

## API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 测试账号

- 张三 / 123456 (技术部)
- 李四 / 123456 (销售部经理)
- 王五 / 123456 (技术部)
- 赵六 / 123456 (销售部)
