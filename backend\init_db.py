#!/usr/bin/env python3
"""数据库初始化脚本"""

import sqlite3
import os

def init_database():
    """初始化数据库"""
    # 删除现有数据库文件
    db_file = 'work_log_system.db'
    if os.path.exists(db_file):
        os.remove(db_file)
        print(f"已删除现有数据库文件: {db_file}")
    
    # 创建新数据库并执行初始化脚本
    conn = sqlite3.connect(db_file)
    try:
        with open('init.sql', 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        conn.executescript(sql_script)
        print("数据库初始化完成")
        
        # 验证数据
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM employee")
        emp_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM project")
        proj_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM work_log")
        log_count = cursor.fetchone()[0]
        
        print(f"数据验证:")
        print(f"  部门数量: {dept_count}")
        print(f"  员工数量: {emp_count}")
        print(f"  项目数量: {proj_count}")
        print(f"  工作日志数量: {log_count}")
        
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    init_database()
