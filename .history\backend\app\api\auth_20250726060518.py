"""认证相关 API"""
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from ..auth import authenticate_employee, create_access_token, get_current_employee, get_password_hash, verify_password
from ..config import settings
from ..database import get_db
from ..schemas.auth import Token, LoginRequest, ChangePasswordRequest
from ..models.employee import Employee

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/login", response_model=Token, summary="员工登录")
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """员工登录接口"""
    employee = authenticate_employee(db, login_data.employee_name, login_data.password)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": employee.employee_name}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/token", response_model=Token, summary="获取访问令牌")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2 兼容的登录接口"""
    employee = authenticate_employee(db, form_data.username, form_data.password)
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": employee.employee_name}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/change-password", summary="修改密码")
async def change_password(
    password_data: ChangePasswordRequest,
    current_employee: Employee = Depends(get_current_employee),
    db: Session = Depends(get_db)
):
    """修改密码接口"""
    # 验证当前密码
    if not verify_password(password_data.current_password, current_employee.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )

    # 更新密码
    current_employee.password = get_password_hash(password_data.new_password)
    db.commit()

    return {"message": "密码修改成功"}
