#!/usr/bin/env python3
"""生成密码哈希"""

from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def generate_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

if __name__ == "__main__":
    # 生成admin123的哈希
    admin_hash = generate_password_hash("admin123")
    print(f"admin123的哈希: {admin_hash}")
    
    # 生成123456的哈希
    user_hash = generate_password_hash("123456")
    print(f"123456的哈希: {user_hash}")
    
    # 验证
    print(f"验证admin123: {verify_password('admin123', admin_hash)}")
    print(f"验证123456: {verify_password('123456', user_hash)}")
