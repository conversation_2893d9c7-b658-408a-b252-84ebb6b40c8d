"""员工管理 API"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload

from ..auth import get_current_employee, get_password_hash
from ..database import get_db
from ..models.employee import Employee
from ..schemas.employee import Employee as EmployeeSchema, EmployeeCreate, EmployeeUpdate

router = APIRouter(prefix="/employees", tags=["员工管理"])


@router.get("/me", response_model=EmployeeSchema, summary="获取当前员工信息")
async def get_current_employee_info(
    current_employee: Employee = Depends(get_current_employee),
    db: Session = Depends(get_db)
):
    """获取当前员工信息"""
    employee = db.query(Employee).options(joinedload(Employee.department)).filter(
        Employee.id == current_employee.id,
        Employee.delete_flag == False
    ).first()
    return employee


@router.get("/", response_model=List[EmployeeSchema], summary="获取员工列表")
async def get_employees(
    skip: int = 0,
    limit: int = 100,
    department_id: int = None,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取员工列表"""
    query = db.query(Employee).options(joinedload(Employee.department)).filter(Employee.delete_flag == False)

    if department_id:
        query = query.filter(Employee.department_id == department_id)

    employees = query.offset(skip).limit(limit).all()
    return employees


@router.get("/{employee_id}", response_model=EmployeeSchema, summary="获取员工详情")
async def get_employee(
    employee_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取员工详情"""
    employee = db.query(Employee).options(joinedload(Employee.department)).filter(
        Employee.id == employee_id
    ).first()
    if employee is None:
        raise HTTPException(status_code=404, detail="员工不存在")
    return employee


@router.post("/", response_model=EmployeeSchema, summary="创建员工")
async def create_employee(
    employee: EmployeeCreate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """创建员工"""
    # 检查员工名是否已存在
    existing_employee = db.query(Employee).filter(Employee.employee_name == employee.employee_name).first()
    if existing_employee:
        raise HTTPException(status_code=400, detail="员工名已存在")
    
    # 创建员工
    employee_data = employee.model_dump()
    employee_data["password"] = get_password_hash(employee_data["password"])
    
    db_employee = Employee(**employee_data)
    db.add(db_employee)
    db.commit()
    db.refresh(db_employee)
    
    # 重新查询以包含关联数据
    employee_with_dept = db.query(Employee).options(joinedload(Employee.department)).filter(
        Employee.id == db_employee.id
    ).first()
    
    return employee_with_dept


@router.put("/{employee_id}", response_model=EmployeeSchema, summary="更新员工")
async def update_employee(
    employee_id: int,
    employee: EmployeeUpdate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """更新员工"""
    db_employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if db_employee is None:
        raise HTTPException(status_code=404, detail="员工不存在")
    
    update_data = employee.model_dump(exclude_unset=True)
    
    # 如果更新密码，需要加密
    if "password" in update_data:
        update_data["password"] = get_password_hash(update_data["password"])
    
    # 检查员工名是否已存在（如果要更新员工名）
    if "employee_name" in update_data:
        existing_employee = db.query(Employee).filter(
            Employee.employee_name == update_data["employee_name"],
            Employee.id != employee_id
        ).first()
        if existing_employee:
            raise HTTPException(status_code=400, detail="员工名已存在")
    
    for field, value in update_data.items():
        setattr(db_employee, field, value)
    
    db.commit()
    db.refresh(db_employee)
    
    # 重新查询以包含关联数据
    employee_with_dept = db.query(Employee).options(joinedload(Employee.department)).filter(
        Employee.id == db_employee.id
    ).first()
    
    return employee_with_dept


@router.delete("/{employee_id}", summary="删除员工")
async def delete_employee(
    employee_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """删除员工"""
    if employee_id == current_employee.id:
        raise HTTPException(status_code=400, detail="不能删除自己")
    
    db_employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if db_employee is None:
        raise HTTPException(status_code=404, detail="员工不存在")
    
    db.delete(db_employee)
    db.commit()
    return {"message": "员工删除成功"}
