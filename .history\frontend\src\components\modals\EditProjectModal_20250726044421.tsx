"use client";

import { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, FolderIcon } from "@heroicons/react/24/outline";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import { projectApi, employeeApi } from "@/lib/api";
import type { Project, Employee } from "@/types";

const projectSchema = z.object({
  project_number: z
    .string()
    .min(1, "请输入项目号")
    .max(50, "项目号不能超过50字符"),
  project_type: z
    .string()
    .min(1, "请输入项目类型")
    .max(100, "项目类型不能超过100字符"),
  customer_abbreviation: z
    .string()
    .min(1, "请输入客户简称")
    .max(100, "客户简称不能超过100字符"),
  customer_name: z
    .string()
    .min(1, "请输入客户名称")
    .max(200, "客户名称不能超过200字符"),
  project_name: z
    .string()
    .min(1, "请输入项目名称")
    .max(200, "项目名称不能超过200字符"),
  employee_id: z.number().min(1, "请选择销售经理"),
  remarks: z.string().max(500, "备注不能超过500字符").optional(),
});

interface ProjectFormData {
  project_number: string;
  project_type: string;
  customer_abbreviation: string;
  customer_name: string;
  project_name: string;
  manager_id: number;
  remarks?: string;
}

interface EditProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  project: Project | null;
}

export default function EditProjectModal({
  isOpen,
  onClose,
  onSuccess,
  project,
}: EditProjectModalProps) {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
  });

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoading(true);
        const employeesData = await employeeApi.getAll({ limit: 100 });
        setEmployees(employeesData.filter((emp) => emp.is_sales));
      } catch (error) {
        console.error("获取员工列表失败:", error);
        toast.error("获取员工列表失败");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchEmployees();
    }
  }, [isOpen]);

  useEffect(() => {
    if (project && isOpen) {
      setValue("project_number", project.project_number);
      setValue("project_type", project.project_type);
      setValue("customer_abbreviation", project.customer_abbreviation);
      setValue("customer_name", project.customer_name);
      setValue("project_name", project.project_name);
      setValue("manager_id", project.manager_id);
      setValue("remarks", project.remarks || "");
    }
  }, [project, isOpen, setValue]);

  const onSubmit = async (data: ProjectFormData) => {
    if (!project) return;

    try {
      await projectApi.update(project.id, data);
      toast.success("项目更新成功");
      reset();
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("更新项目失败:", error);
      const message = error.response?.data?.detail || "更新项目失败";
      toast.error(message);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FolderIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold text-gray-900"
                      >
                        编辑项目
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">修改项目信息</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* 项目号 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        项目号 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        {...register("project_number")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.project_number
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        placeholder="PRJ001"
                      />
                      {errors.project_number && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.project_number.message}
                        </p>
                      )}
                    </div>

                    {/* 项目类型 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        项目类型 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        {...register("project_type")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.project_type
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        placeholder="Web开发"
                      />
                      {errors.project_type && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.project_type.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* 客户简称 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        客户简称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        {...register("customer_abbreviation")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.customer_abbreviation
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        placeholder="ABC公司"
                      />
                      {errors.customer_abbreviation && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.customer_abbreviation.message}
                        </p>
                      )}
                    </div>

                    {/* 客户名称 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        客户名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        {...register("customer_name")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.customer_name
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        placeholder="ABC科技有限公司"
                      />
                      {errors.customer_name && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.customer_name.message}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      项目名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      {...register("project_name")}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.project_name
                          ? "border-red-300"
                          : "border-gray-300"
                      }`}
                      placeholder="企业官网建设项目"
                    />
                    {errors.project_name && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.project_name.message}
                      </p>
                    )}
                  </div>

                  {/* 销售经理 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      销售经理 <span className="text-red-500">*</span>
                    </label>
                    <select
                      {...register("manager_id", { valueAsNumber: true })}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.manager_id ? "border-red-300" : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value={0}>请选择销售经理</option>
                      {employees.map((employee) => (
                        <option key={employee.id} value={employee.id}>
                          {employee.employee_name} -{" "}
                          {employee.department?.department_name}
                        </option>
                      ))}
                    </select>
                    {errors.manager_id && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.manager_id.message}
                      </p>
                    )}
                  </div>

                  {/* 备注 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      备注
                    </label>
                    <textarea
                      {...register("remarks")}
                      rows={3}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.remarks ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="可选的项目备注信息..."
                    />
                    {errors.remarks && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.remarks.message}
                      </p>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || isLoading}
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          更新中...
                        </div>
                      ) : (
                        "更新项目"
                      )}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
