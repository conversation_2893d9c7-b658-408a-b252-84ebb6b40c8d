'use client';

import { useState, useEffect } from 'react';
import { 
  ClipboardDocumentListIcon,
  FolderIcon,
  UserGroupIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';
import { workLogApi, projectApi, employeeApi } from '@/lib/api';
import { formatHours, formatDate, getTodayString } from '@/lib/utils';
import type { WorkLogStatistics, WorkLog } from '@/types';

interface DashboardStats {
  totalHours: number;
  totalLogs: number;
  totalProjects: number;
  totalEmployees: number;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalHours: 0,
    totalLogs: 0,
    totalProjects: 0,
    totalEmployees: 0,
  });
  const [recentLogs, setRecentLogs] = useState<WorkLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // 获取工时统计
        const workLogStats = await workLogApi.getStatistics({
          start_date: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          end_date: getTodayString(),
        });

        // 获取最近的工作日志
        const logs = await workLogApi.getMy({ limit: 5 });

        // 如果是销售人员，获取更多统计信息
        let projectCount = 0;
        let employeeCount = 0;
        
        if (user?.is_sales) {
          const [projects, employees] = await Promise.all([
            projectApi.getAll({ limit: 1000 }),
            employeeApi.getAll({ limit: 1000 }),
          ]);
          projectCount = projects.length;
          employeeCount = employees.length;
        }

        setStats({
          totalHours: workLogStats.total_hours,
          totalLogs: workLogStats.total_logs,
          totalProjects: projectCount,
          totalEmployees: employeeCount,
        });
        setRecentLogs(logs);
      } catch (error) {
        console.error('获取仪表板数据失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const statCards = [
    {
      name: '本月工时',
      value: formatHours(stats.totalHours),
      icon: ClockIcon,
      color: 'bg-blue-500',
    },
    {
      name: '工作日志',
      value: stats.totalLogs.toString(),
      icon: ClipboardDocumentListIcon,
      color: 'bg-green-500',
    },
    ...(user?.is_sales ? [
      {
        name: '项目数量',
        value: stats.totalProjects.toString(),
        icon: FolderIcon,
        color: 'bg-yellow-500',
      },
      {
        name: '员工数量',
        value: stats.totalEmployees.toString(),
        icon: UserGroupIcon,
        color: 'bg-purple-500',
      },
    ] : []),
  ];

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-gray-300 rounded"></div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 欢迎信息 */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">
              欢迎回来，{user?.employee_name}！
            </h1>
            <p className="mt-2 text-blue-100">
              今天是 {formatDate(new Date(), 'yyyy年MM月dd日')}，祝您工作愉快！
            </p>
            <div className="mt-4 flex items-center space-x-4">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-400 rounded-full mr-2"></div>
                <span className="text-sm">系统运行正常</span>
              </div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-yellow-400 rounded-full mr-2"></div>
                <span className="text-sm">{user?.department?.department_name}</span>
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="h-24 w-24 bg-white/20 rounded-full flex items-center justify-center">
              <svg className="h-12 w-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((item) => (
          <div key={item.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-2 rounded-md ${item.color}`}>
                    <item.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {item.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {item.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 最近的工作日志 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            最近的工作日志
          </h3>
          {recentLogs.length > 0 ? (
            <div className="flow-root">
              <ul className="-mb-8">
                {recentLogs.map((log, logIdx) => (
                  <li key={log.id}>
                    <div className="relative pb-8">
                      {logIdx !== recentLogs.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white">
                            <ClipboardDocumentListIcon className="h-5 w-5 text-white" />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-500">
                              在项目{' '}
                              <span className="font-medium text-gray-900">
                                {log.project?.project_name}
                              </span>{' '}
                              工作了 {formatHours(log.work_hours)}
                            </p>
                            <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                              {log.work_content}
                            </p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            {formatDate(log.work_date)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-6">
              <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无工作日志</h3>
              <p className="mt-1 text-sm text-gray-500">
                开始记录您的第一条工作日志吧！
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
