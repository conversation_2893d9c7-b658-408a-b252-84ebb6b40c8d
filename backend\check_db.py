#!/usr/bin/env python3
"""检查数据库文件"""

import sqlite3
import os

def check_db():
    """检查数据库文件"""
    db_file = 'work_log_system.db'
    
    if os.path.exists(db_file):
        print(f"数据库文件存在: {db_file}")
        print(f"文件大小: {os.path.getsize(db_file)} bytes")
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 检查表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"表: {[table[0] for table in tables]}")
            
            if 'employee' in [table[0] for table in tables]:
                cursor.execute("SELECT COUNT(*) FROM employee")
                count = cursor.fetchone()[0]
                print(f"员工数量: {count}")
                
                if count > 0:
                    cursor.execute("SELECT employee_name FROM employee")
                    names = cursor.fetchall()
                    print(f"员工名称: {[name[0] for name in names]}")
            
            conn.close()
            
        except Exception as e:
            print(f"数据库访问错误: {e}")
    else:
        print(f"数据库文件不存在: {db_file}")

if __name__ == "__main__":
    check_db()
