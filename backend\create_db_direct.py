#!/usr/bin/env python3
"""直接创建数据库"""

import sqlite3
import os
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_database():
    """直接创建数据库"""
    db_file = 'work_log_system.db'
    
    # 删除现有文件
    if os.path.exists(db_file):
        os.remove(db_file)
        print(f"已删除现有数据库文件: {db_file}")
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    try:
        # 创建部门表
        cursor.execute('''
        CREATE TABLE department (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            department_name VARCHAR(100) NOT NULL,
            delete_flag BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建员工表
        cursor.execute('''
        CREATE TABLE employee (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_name VARCHAR(128) NOT NULL,
            password VARCHAR(128) NOT NULL,
            is_sales BOOLEAN NOT NULL DEFAULT 0,
            is_admin BOOLEAN NOT NULL DEFAULT 0,
            department_id INTEGER NOT NULL,
            remarks VARCHAR(500),
            delete_flag BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY(department_id) REFERENCES department (id) ON DELETE RESTRICT
        )
        ''')
        
        # 创建项目表
        cursor.execute('''
        CREATE TABLE project (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_number VARCHAR(50) NOT NULL UNIQUE,
            project_type VARCHAR(100) NOT NULL,
            customer_abbreviation VARCHAR(100) NOT NULL,
            customer_name VARCHAR(200) NOT NULL,
            project_name VARCHAR(200) NOT NULL,
            employee_id INTEGER NOT NULL,
            remarks VARCHAR(500),
            delete_flag BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY(employee_id) REFERENCES employee (id) ON DELETE RESTRICT
        )
        ''')
        
        # 创建工作日志表
        cursor.execute('''
        CREATE TABLE work_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            work_date DATE NOT NULL,
            employee_id INTEGER NOT NULL,
            project_number VARCHAR(50) NOT NULL,
            work_hours DECIMAL(4,2) NOT NULL,
            work_content TEXT NOT NULL,
            remarks VARCHAR(500),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY(employee_id) REFERENCES employee (id) ON DELETE RESTRICT,
            FOREIGN KEY(project_number) REFERENCES project (project_number) ON DELETE RESTRICT
        )
        ''')
        
        # 插入部门数据
        departments = [
            (1, '技术部'),
            (2, '销售部'),
            (3, '市场部'),
            (4, '人事部'),
            (5, '财务部')
        ]
        cursor.executemany('INSERT INTO department (id, department_name) VALUES (?, ?)', departments)
        
        # 生成密码哈希
        admin_hash = pwd_context.hash("admin123")
        user_hash = pwd_context.hash("123456")
        
        # 插入员工数据
        employees = [
            (1, 'admin', admin_hash, 1, 1, 2, '系统管理员'),
            (2, '张三', user_hash, 0, 0, 1, '技术部主管'),
            (3, '李四', user_hash, 1, 0, 2, '销售经理'),
            (4, '王五', user_hash, 0, 0, 1, '前端开发工程师'),
            (5, '赵六', user_hash, 1, 0, 2, '销售代表')
        ]
        cursor.executemany('INSERT INTO employee (id, employee_name, password, is_sales, is_admin, department_id, remarks) VALUES (?, ?, ?, ?, ?, ?, ?)', employees)
        
        # 插入项目数据
        projects = [
            (1, 'PRJ001', 'Web开发', 'ABC公司', 'ABC科技有限公司', '企业官网建设项目', 3, '企业官网重构项目'),
            (2, 'PRJ002', '移动应用', 'XYZ集团', 'XYZ集团有限公司', '移动端APP开发', 3, '电商类移动应用'),
            (3, 'PRJ003', '系统集成', 'DEF企业', 'DEF企业管理有限公司', 'ERP系统实施', 3, 'ERP系统定制开发')
        ]
        cursor.executemany('INSERT INTO project (id, project_number, project_type, customer_abbreviation, customer_name, project_name, employee_id, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', projects)
        
        # 插入工作日志数据
        work_logs = [
            ('2025-07-25', 2, 'PRJ001', 8.0, '完成首页设计和开发', '进度正常'),
            ('2025-07-25', 4, 'PRJ001', 7.5, '实现用户登录功能', '需要优化性能'),
            ('2025-07-24', 2, 'PRJ002', 6.0, '移动端界面适配', ''),
            ('2025-07-24', 4, 'PRJ002', 8.0, 'API接口开发', '已完成用户模块'),
            ('2025-07-23', 2, 'PRJ003', 4.0, 'ERP系统需求分析', '与客户沟通确认'),
            ('2025-07-23', 3, 'PRJ001', 2.0, '项目进度跟踪', '客户满意度较高'),
            ('2025-07-22', 2, 'PRJ001', 8.0, '数据库设计优化', '性能提升明显'),
            ('2025-07-22', 4, 'PRJ003', 6.5, '系统架构设计', '技术方案已确定'),
            ('2025-07-21', 2, 'PRJ002', 7.0, '移动端测试', '发现并修复3个bug'),
            ('2025-07-21', 4, 'PRJ001', 8.0, '前端组件开发', '完成80%功能')
        ]
        cursor.executemany('INSERT INTO work_log (work_date, employee_id, project_number, work_hours, work_content, remarks) VALUES (?, ?, ?, ?, ?, ?)', work_logs)
        
        conn.commit()
        print("数据库创建成功")
        
        # 验证数据
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM employee")
        emp_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM project")
        proj_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM work_log")
        log_count = cursor.fetchone()[0]
        
        print(f"数据验证:")
        print(f"  部门数量: {dept_count}")
        print(f"  员工数量: {emp_count}")
        print(f"  项目数量: {proj_count}")
        print(f"  工作日志数量: {log_count}")
        
        # 验证admin用户
        cursor.execute("SELECT employee_name, password FROM employee WHERE employee_name = 'admin'")
        admin = cursor.fetchone()
        if admin:
            print(f"  admin用户: {admin[0]}")
            is_valid = pwd_context.verify("admin123", admin[1])
            print(f"  密码验证: {is_valid}")
        
    except Exception as e:
        print(f"数据库创建失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    create_database()
