"use client";

import { useState, useEffect } from "react";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  ClockIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import toast from "react-hot-toast";
import { useAuth } from "@/hooks/useAuth";
import { workLogApi, projectApi } from "@/lib/api";
import { formatHours, formatDate } from "@/lib/utils";
import CreateWorkLogModal from "@/components/modals/CreateWorkLogModal";
import EditWorkLogModal from "@/components/modals/EditWorkLogModal";
import type { WorkLog, Project } from "@/types";

export default function WorkLogsPage() {
  const { user } = useAuth();
  const [workLogs, setWorkLogs] = useState<WorkLog[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProject, setSelectedProject] = useState("");
  const [dateRange, setDateRange] = useState({
    start: "",
    end: "",
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedWorkLog, setSelectedWorkLog] = useState<WorkLog | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [logsData, projectsData] = await Promise.all([
        // 管理员可以查看所有工作日志，普通用户只能查看自己的
        user?.is_admin
          ? workLogApi.getAll({ limit: 100 })
          : workLogApi.getMy({ limit: 50 }),
        projectApi.getAll({ limit: 100 }),
      ]);
      setWorkLogs(logsData);
      setProjects(projectsData);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = (workLog: WorkLog) => {
    setSelectedWorkLog(workLog);
    setShowEditModal(true);
  };

  const handleDelete = async (workLog: WorkLog) => {
    if (window.confirm(`确定要删除这条工作日志吗？`)) {
      try {
        await workLogApi.delete(workLog.id);
        toast.success("工作日志删除成功");
        fetchData();
      } catch (error: any) {
        console.error("删除工作日志失败:", error);
        const message = error.response?.data?.detail || "删除工作日志失败";
        toast.error(message);
      }
    }
  };

  const filteredLogs = workLogs.filter((log) => {
    const matchesSearch =
      log.work_content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.project?.project_name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
    const matchesProject =
      !selectedProject || log.project_number === selectedProject;
    const matchesDateRange =
      (!dateRange.start || log.work_date >= dateRange.start) &&
      (!dateRange.end || log.work_date <= dateRange.end);

    return matchesSearch && matchesProject && matchesDateRange;
  });

  const totalHours = filteredLogs.reduce(
    (sum, log) => sum + Number(log.work_hours),
    0
  );

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-300 rounded w-1/4"></div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
        <div className="h-96 bg-gray-300 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">我的工作日志</h1>
          <p className="mt-2 text-sm text-gray-600">
            记录和管理您的日常工作内容
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5"
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          新建日志
        </button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-blue-50">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总工时</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatHours(totalHours)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-green-50">
                  <DocumentTextIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">日志数量</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredLogs.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-purple-50">
                  <CalendarIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">平均工时</p>
                <p className="text-2xl font-bold text-gray-900">
                  {filteredLogs.length > 0
                    ? formatHours(totalHours / filteredLogs.length)
                    : "0小时"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              搜索
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="搜索工作内容或项目..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目
            </label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
            >
              <option value="">全部项目</option>
              {projects.map((project) => (
                <option
                  key={project.project_number}
                  value={project.project_number}
                >
                  {project.project_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              开始日期
            </label>
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, start: e.target.value }))
              }
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              结束日期
            </label>
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, end: e.target.value }))
              }
            />
          </div>
        </div>
      </div>

      {/* 工作日志列表 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">工作日志列表</h3>
        </div>

        {filteredLogs.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredLogs.map((log) => (
              <div
                key={log.id}
                className="p-6 hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {log.project?.project_name || "未知项目"}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {formatHours(log.work_hours)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(log.work_date)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-xs text-gray-500">客户:</span>
                      <span className="text-xs font-medium text-gray-700 truncate">
                        {log.project?.customer_name
                          ? log.project.customer_name.length > 20
                            ? `${log.project.customer_name.substring(0, 20)}...`
                            : log.project.customer_name
                          : "未知客户"}
                      </span>
                      {user?.is_admin && log.employee && (
                        <>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">员工:</span>
                          <span className="text-xs font-medium text-gray-700">
                            {log.employee.employee_name}
                          </span>
                        </>
                      )}
                    </div>
                    <p className="text-gray-900 mb-2">{log.work_content}</p>
                    {log.remarks && (
                      <p className="text-sm text-gray-500">
                        备注: {log.remarks}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleEdit(log)}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => handleDelete(log)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              暂无工作日志
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedProject || dateRange.start || dateRange.end
                ? "没有找到符合条件的日志"
                : "开始记录您的第一条工作日志吧！"}
            </p>
          </div>
        )}
      </div>

      {/* 新建工作日志模态框 */}
      <CreateWorkLogModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={fetchData}
      />

      {/* 编辑工作日志模态框 */}
      <EditWorkLogModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={fetchData}
        workLog={selectedWorkLog}
      />
    </div>
  );
}
