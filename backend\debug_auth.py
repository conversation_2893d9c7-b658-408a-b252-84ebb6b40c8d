#!/usr/bin/env python3
"""调试认证问题"""

import sqlite3
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def debug_auth():
    """调试认证问题"""
    conn = sqlite3.connect('work_log_system.db')
    cursor = conn.cursor()
    
    # 检查表结构
    cursor.execute("PRAGMA table_info(employee)")
    columns = cursor.fetchall()
    print("employee表结构:")
    for col in columns:
        print(f"  {col[1]} {col[2]} (NOT NULL: {col[3]}, DEFAULT: {col[4]})")
    
    # 查询admin用户
    cursor.execute('SELECT * FROM employee WHERE employee_name = ?', ('admin',))
    admin = cursor.fetchone()
    
    if admin:
        print(f"\nadmin用户数据:")
        print(f"  ID: {admin[0]}")
        print(f"  用户名: {admin[1]}")
        print(f"  密码哈希: {admin[2][:50]}...")
        print(f"  is_sales: {admin[3]}")
        print(f"  is_admin: {admin[4]}")
        print(f"  department_id: {admin[5]}")
        print(f"  remarks: {admin[6]}")
        if len(admin) > 7:
            print(f"  delete_flag: {admin[7]}")
        
        # 验证密码
        is_valid = pwd_context.verify("admin123", admin[2])
        print(f"  密码验证(admin123): {is_valid}")
        
        is_valid_123456 = pwd_context.verify("123456", admin[2])
        print(f"  密码验证(123456): {is_valid_123456}")
    else:
        print("未找到admin用户")
    
    conn.close()

if __name__ == "__main__":
    debug_auth()
