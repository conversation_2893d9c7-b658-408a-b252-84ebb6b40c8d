#!/usr/bin/env python3
"""测试API接口"""

import requests
import json

def test_api():
    """测试API接口"""
    base_url = "http://localhost:8000/api/v1"
    
    # 测试登录
    print("测试登录...")
    login_data = {
        "employee_name": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        print(f"登录响应状态: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"获取到token: {token[:50]}...")
            
            # 测试项目API
            headers = {"Authorization": f"Bearer {token}"}
            
            print("\n测试项目列表...")
            projects_response = requests.get(f"{base_url}/projects", headers=headers)
            print(f"项目列表响应状态: {projects_response.status_code}")
            if projects_response.status_code == 200:
                projects = projects_response.json()
                print(f"项目数量: {len(projects)}")
                for project in projects:
                    print(f"  - {project['project_name']} (负责人ID: {project.get('employee_id', 'N/A')})")
            else:
                print(f"项目列表错误: {projects_response.text}")
            
            print("\n测试员工列表...")
            employees_response = requests.get(f"{base_url}/employees", headers=headers)
            print(f"员工列表响应状态: {employees_response.status_code}")
            if employees_response.status_code == 200:
                employees = employees_response.json()
                print(f"员工数量: {len(employees)}")
                for employee in employees:
                    print(f"  - {employee['employee_name']} (部门ID: {employee.get('department_id', 'N/A')})")
            else:
                print(f"员工列表错误: {employees_response.text}")
                
        else:
            print("登录失败")
            
    except Exception as e:
        print(f"API测试失败: {e}")

if __name__ == "__main__":
    test_api()
