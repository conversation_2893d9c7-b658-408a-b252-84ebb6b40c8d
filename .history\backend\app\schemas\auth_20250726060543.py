"""认证相关模式"""
from typing import Optional
from pydantic import BaseModel


class Token(BaseModel):
    """访问令牌"""
    access_token: str
    token_type: str


class TokenData(BaseModel):
    """令牌数据"""
    employee_name: Optional[str] = None


class LoginRequest(BaseModel):
    """登录请求"""
    employee_name: str
    password: str


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    current_password: str
    new_password: str
