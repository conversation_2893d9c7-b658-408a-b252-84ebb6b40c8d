#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import Base, engine
from app.models import Department, Employee, Project, WorkLog
from app.auth import get_password_hash

def test_connection():
    """测试数据库连接"""
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def create_tables():
    """创建数据库表"""
    try:
        # 删除所有表并重新创建
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表重新创建成功")
        return True
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        return False

def insert_sample_data():
    """插入示例数据"""
    from sqlalchemy.orm import sessionmaker
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 清空现有数据
        db.query(WorkLog).delete()
        db.query(Project).delete()
        db.query(Employee).delete()
        db.query(Department).delete()
        db.commit()
        print("🗑️  清空现有数据")
        
        # 插入部门数据
        departments = [
            Department(department_name="技术部"),
            Department(department_name="销售部"),
            Department(department_name="市场部"),
            Department(department_name="人事部"),
            Department(department_name="财务部"),
        ]
        
        for dept in departments:
            db.add(dept)
        db.commit()
        
        # 获取部门ID
        tech_dept = db.query(Department).filter(Department.department_name == "技术部").first()
        sales_dept = db.query(Department).filter(Department.department_name == "销售部").first()
        
        # 插入员工数据
        employees = [
            Employee(
                employee_name="admin",
                password=get_password_hash("admin123"),
                is_sales=True,
                is_admin=True,
                department_id=sales_dept.id,
                remarks="系统管理员"
            ),
            Employee(
                employee_name="张三",
                password=get_password_hash("123456"),
                is_sales=False,
                is_admin=False,
                department_id=tech_dept.id,
                remarks="技术部主管"
            ),
            Employee(
                employee_name="李四",
                password=get_password_hash("123456"),
                is_sales=True,
                is_admin=False,
                department_id=sales_dept.id,
                remarks="销售经理"
            ),
            Employee(
                employee_name="王五",
                password=get_password_hash("123456"),
                is_sales=False,
                is_admin=False,
                department_id=tech_dept.id,
                remarks="前端开发工程师"
            ),
            Employee(
                employee_name="赵六",
                password=get_password_hash("123456"),
                is_sales=True,
                is_admin=False,
                department_id=sales_dept.id,
                remarks="销售代表"
            ),
        ]
        
        for emp in employees:
            db.add(emp)
        db.commit()
        
        # 获取员工ID
        manager = db.query(Employee).filter(Employee.employee_name == "李四").first()
        
        # 插入项目数据
        projects = [
            Project(
                project_number="PRJ001",
                project_type="Web开发",
                customer_abbreviation="ABC公司",
                customer_name="ABC科技有限公司",
                project_name="企业官网建设项目",
                manager_id=manager.id,
                remarks="企业官网重构项目"
            ),
            Project(
                project_number="PRJ002",
                project_type="移动应用",
                customer_abbreviation="XYZ集团",
                customer_name="XYZ集团有限公司",
                project_name="移动端APP开发",
                manager_id=manager.id,
                remarks="电商类移动应用"
            ),
            Project(
                project_number="PRJ003",
                project_type="系统集成",
                customer_abbreviation="DEF企业",
                customer_name="DEF企业管理有限公司",
                project_name="ERP系统实施",
                manager_id=manager.id,
                remarks="ERP系统定制开发"
            ),
        ]
        
        for proj in projects:
            db.add(proj)
        db.commit()
        
        print("✅ 示例数据插入成功")
        print("\n📋 测试账号信息:")
        print("员工姓名: admin, 密码: admin123 (系统管理员)")
        print("员工姓名: 张三, 密码: 123456 (技术部)")
        print("员工姓名: 李四, 密码: 123456 (销售部经理)")
        print("员工姓名: 王五, 密码: 123456 (技术部)")
        print("员工姓名: 赵六, 密码: 123456 (销售部)")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例数据插入失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始数据库设置...")
    print(f"📍 数据库URL: {settings.database_url}")
    
    # 测试连接
    if not test_connection():
        print("\n❌ 请检查数据库配置和连接")
        return False
    
    # 创建表
    if not create_tables():
        return False
    
    # 插入示例数据
    if not insert_sample_data():
        return False
    
    print("\n🎉 数据库设置完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
