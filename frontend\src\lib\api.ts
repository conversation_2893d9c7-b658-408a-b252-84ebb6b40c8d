import axios, { AxiosInstance, AxiosResponse } from "axios";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import type {
  LoginRequest,
  Token,
  User,
  Department,
  DepartmentCreate,
  DepartmentUpdate,
  Employee,
  EmployeeCreate,
  EmployeeUpdate,
  Project,
  ProjectCreate,
  ProjectUpdate,
  WorkLog,
  WorkLogCreate,
  WorkLogUpdate,
  WorkLogStatistics,
  WorkLogQueryParams,
} from "@/types";

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器 - 添加认证 token
apiClient.interceptors.request.use(
  (config) => {
    const token = Cookies.get("access_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除 token 并跳转到登录页
      Cookies.remove("access_token");
      window.location.href = "/login";
    } else if (error.response?.data?.detail) {
      // 显示服务器错误信息
      toast.error(error.response.data.detail);
    } else {
      // 显示通用错误信息
      toast.error("请求失败，请稍后重试");
    }
    return Promise.reject(error);
  }
);

// 认证 API
export const authApi = {
  login: async (data: LoginRequest): Promise<Token> => {
    const response = await apiClient.post<Token>("/auth/login", data);
    return response.data;
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get<User>("/employees/me");
    return response.data;
  },

  changePassword: async (data: {
    current_password: string;
    new_password: string;
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>(
      "/auth/change-password",
      data
    );
    return response.data;
  },
};

// 部门 API
export const departmentApi = {
  getAll: async (): Promise<Department[]> => {
    const response = await apiClient.get<Department[]>("/departments/");
    return response.data;
  },

  getById: async (id: number): Promise<Department> => {
    const response = await apiClient.get<Department>(`/departments/${id}`);
    return response.data;
  },

  create: async (data: DepartmentCreate): Promise<Department> => {
    const response = await apiClient.post<Department>("/departments/", data);
    return response.data;
  },

  update: async (id: number, data: DepartmentUpdate): Promise<Department> => {
    const response = await apiClient.put<Department>(
      `/departments/${id}`,
      data
    );
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/departments/${id}`);
  },
};

// 员工 API
export const employeeApi = {
  getAll: async (params?: {
    skip?: number;
    limit?: number;
    department_id?: number;
  }): Promise<Employee[]> => {
    const response = await apiClient.get<Employee[]>("/employees/", { params });
    return response.data;
  },

  getById: async (id: number): Promise<Employee> => {
    const response = await apiClient.get<Employee>(`/employees/${id}`);
    return response.data;
  },

  create: async (data: EmployeeCreate): Promise<Employee> => {
    const response = await apiClient.post<Employee>("/employees/", data);
    return response.data;
  },

  update: async (id: number, data: EmployeeUpdate): Promise<Employee> => {
    const response = await apiClient.put<Employee>(`/employees/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/employees/${id}`);
  },
};

// 项目 API
export const projectApi = {
  getAll: async (params?: {
    skip?: number;
    limit?: number;
    manager_id?: number;
    project_type?: string;
  }): Promise<Project[]> => {
    const response = await apiClient.get<Project[]>("/projects/", { params });
    return response.data;
  },

  getById: async (id: number): Promise<Project> => {
    const response = await apiClient.get<Project>(`/projects/${id}`);
    return response.data;
  },

  getByNumber: async (projectNumber: string): Promise<Project> => {
    const response = await apiClient.get<Project>(
      `/projects/by-number/${projectNumber}`
    );
    return response.data;
  },

  create: async (data: ProjectCreate): Promise<Project> => {
    const response = await apiClient.post<Project>("/projects/", data);
    return response.data;
  },

  update: async (id: number, data: ProjectUpdate): Promise<Project> => {
    const response = await apiClient.put<Project>(`/projects/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/projects/${id}`);
  },
};

// 工作日志 API
export const workLogApi = {
  getAll: async (params?: WorkLogQueryParams): Promise<WorkLog[]> => {
    const response = await apiClient.get<WorkLog[]>("/work-logs/", { params });
    return response.data;
  },

  getMy: async (
    params?: Omit<WorkLogQueryParams, "employee_id">
  ): Promise<WorkLog[]> => {
    const response = await apiClient.get<WorkLog[]>("/work-logs/my", {
      params,
    });
    return response.data;
  },

  getById: async (id: number): Promise<WorkLog> => {
    const response = await apiClient.get<WorkLog>(`/work-logs/${id}`);
    return response.data;
  },

  getStatistics: async (
    params?: Omit<WorkLogQueryParams, "skip" | "limit">
  ): Promise<WorkLogStatistics> => {
    const response = await apiClient.get<WorkLogStatistics>(
      "/work-logs/statistics",
      { params }
    );
    return response.data;
  },

  create: async (data: WorkLogCreate): Promise<WorkLog> => {
    const response = await apiClient.post<WorkLog>("/work-logs/", data);
    return response.data;
  },

  update: async (id: number, data: WorkLogUpdate): Promise<WorkLog> => {
    const response = await apiClient.put<WorkLog>(`/work-logs/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/work-logs/${id}`);
  },
};

export default apiClient;
