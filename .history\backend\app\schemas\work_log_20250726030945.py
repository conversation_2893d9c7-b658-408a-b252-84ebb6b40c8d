"""工作日志模式"""
from datetime import date, datetime
from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, ConfigDict, Field
from .employee import Employee
from .project import Project


class WorkLogBase(BaseModel):
    """工作日志基础模式"""
    work_date: date
    employee_id: int
    project_number: str
    work_hours: Decimal = Field(..., ge=0, le=24, decimal_places=2)
    work_content: str
    remarks: Optional[str] = None


class WorkLogCreate(WorkLogBase):
    """创建工作日志模式"""
    pass


class WorkLogUpdate(BaseModel):
    """更新工作日志模式"""
    work_date: Optional[date] = None
    employee_id: Optional[int] = None
    project_number: Optional[str] = None
    work_hours: Optional[Decimal] = Field(None, ge=0, le=24, decimal_places=2)
    work_content: Optional[str] = None
    remarks: Optional[str] = None


class WorkLog(WorkLogBase):
    """工作日志模式"""
    model_config = ConfigDict(from_attributes=True, json_encoders={
        Decimal: float  # 将 Decimal 序列化为 float
    })

    id: int
    created_at: datetime
    updated_at: datetime
    employee: Optional[Employee] = None
    project: Optional[Project] = None
