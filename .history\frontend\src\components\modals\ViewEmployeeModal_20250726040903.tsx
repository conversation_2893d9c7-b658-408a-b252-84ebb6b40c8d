'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, UserIcon, BuildingOfficeIcon, CalendarIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import type { Employee } from '@/types';

interface ViewEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  employee: Employee | null;
}

export default function ViewEmployeeModal({ isOpen, onClose, employee }: ViewEmployeeModalProps) {
  if (!employee) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <UserIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title as="h3" className="text-lg font-semibold text-gray-900">
                        员工详情
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">查看员工详细信息</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <UserIcon className="h-4 w-4 mr-2" />
                      基本信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          员工姓名
                        </label>
                        <p className="mt-1 text-sm text-gray-900">{employee.employee_name}</p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          员工ID
                        </label>
                        <p className="mt-1 text-sm text-gray-900">#{employee.id}</p>
                      </div>
                    </div>
                  </div>

                  {/* 部门信息 */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                      部门信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          所属部门
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {employee.department?.department_name || '未知部门'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          部门ID
                        </label>
                        <p className="mt-1 text-sm text-gray-900">#{employee.department_id}</p>
                      </div>
                    </div>
                  </div>

                  {/* 权限信息 */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <ShieldCheckIcon className="h-4 w-4 mr-2" />
                      权限信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          员工角色
                        </label>
                        <div className="mt-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            employee.is_sales 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {employee.is_sales ? '销售人员' : '普通员工'}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          管理员权限
                        </label>
                        <div className="mt-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            employee.is_admin 
                              ? 'bg-red-100 text-red-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {employee.is_admin ? '管理员' : '普通权限'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 时间信息 */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      时间信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          入职时间
                        </label>
                        <p className="mt-1 text-sm text-gray-900">{formatDate(employee.created_at)}</p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          信息更新时间
                        </label>
                        <p className="mt-1 text-sm text-gray-900">{formatDate(employee.updated_at)}</p>
                      </div>
                    </div>
                  </div>

                  {/* 备注信息 */}
                  {employee.remarks && (
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                        <UserIcon className="h-4 w-4 mr-2" />
                        备注信息
                      </h4>
                      <p className="text-sm text-gray-700">{employee.remarks}</p>
                    </div>
                  )}

                  {/* 权限说明 */}
                  <div className="bg-indigo-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">权限说明</h4>
                    <div className="space-y-2 text-xs text-gray-600">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                        <span>普通员工：只能查看和管理自己的工作日志</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        <span>销售人员：可以管理项目、员工、部门等信息</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                        <span>管理员：具有最高权限，可以查看所有员工的工作日志</span>
                      </div>
                    </div>
                  </div>

                  {/* 关闭按钮 */}
                  <div className="flex items-center justify-end pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={onClose}
                    >
                      关闭
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
