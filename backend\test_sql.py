#!/usr/bin/env python3
"""测试SQL脚本"""

import sqlite3

def test_sql():
    """测试SQL脚本"""
    try:
        conn = sqlite3.connect(':memory:')  # 使用内存数据库测试
        
        with open('init.sql', 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        print("开始执行SQL脚本...")
        conn.executescript(sql_script)
        print("SQL脚本执行成功")
        
        # 检查表是否创建成功
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"创建的表: {[table[0] for table in tables]}")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM employee")
        emp_count = cursor.fetchone()[0]
        print(f"员工数量: {emp_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"SQL脚本执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql()
