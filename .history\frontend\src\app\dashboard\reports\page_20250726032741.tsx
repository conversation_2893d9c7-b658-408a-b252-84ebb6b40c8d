"use client";

import { useState, useEffect } from "react";
import {
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon,
  FolderIcon,
  CalendarIcon,
  DocumentChartBarIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { workLog<PERSON>pi, projectApi, employeeApi } from "@/lib/api";
import { formatHours, formatDate } from "@/lib/utils";
import type { WorkLogStatistics, WorkLog, Project, Employee } from "@/types";

interface ReportData {
  totalStats: WorkLogStatistics;
  monthlyStats: { month: string; hours: number; logs: number }[];
  projectStats: { project: string; hours: number; logs: number }[];
  employeeStats: { employee: string; hours: number; logs: number }[];
  recentLogs: WorkLog[];
}

export default function ReportsPage() {
  const { user } = useAuth();
  const [reportData, setReportData] = useState<ReportData>({
    totalStats: { total_hours: 0, total_logs: 0, avg_hours: 0 },
    monthlyStats: [],
    projectStats: [],
    employeeStats: [],
    recentLogs: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });
  const [selectedEmployee, setSelectedEmployee] = useState("");
  const [selectedProject, setSelectedProject] = useState("");
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // 获取基础数据
        const [employeesData, projectsData] = await Promise.all([
          employeeApi.getAll({ limit: 100 }),
          projectApi.getAll({ limit: 100 }),
        ]);
        setEmployees(employeesData);
        setProjects(projectsData);

        // 获取统计数据
        const params = {
          start_date: dateRange.start,
          end_date: dateRange.end,
          ...(selectedEmployee && { employee_id: parseInt(selectedEmployee) }),
          ...(selectedProject && { project_number: selectedProject }),
        };

        const [totalStats, allLogs] = await Promise.all([
          workLogApi.getStatistics(params),
          user?.is_sales || user?.is_admin
            ? workLogApi.getAll({ ...params, limit: 1000 })
            : workLogApi.getMy({ ...params, limit: 1000 }),
        ]);

        // 处理月度统计
        const monthlyMap = new Map<string, { hours: number; logs: number }>();
        allLogs.forEach((log) => {
          const month = log.work_date.substring(0, 7); // YYYY-MM
          const current = monthlyMap.get(month) || { hours: 0, logs: 0 };
          monthlyMap.set(month, {
            hours: current.hours + Number(log.work_hours),
            logs: current.logs + 1,
          });
        });

        const monthlyStats = Array.from(monthlyMap.entries())
          .map(([month, data]) => ({ month, ...data }))
          .sort((a, b) => a.month.localeCompare(b.month));

        // 处理项目统计
        const projectMap = new Map<string, { hours: number; logs: number }>();
        allLogs.forEach((log) => {
          const projectName = log.project?.project_name || log.project_number;
          const current = projectMap.get(projectName) || { hours: 0, logs: 0 };
          projectMap.set(projectName, {
            hours: current.hours + Number(log.work_hours),
            logs: current.logs + 1,
          });
        });

        const projectStats = Array.from(projectMap.entries())
          .map(([project, data]) => ({ project, ...data }))
          .sort((a, b) => b.hours - a.hours)
          .slice(0, 10);

        // 处理员工统计（销售人员和管理员可见）
        let employeeStats: { employee: string; hours: number; logs: number }[] =
          [];
        if (user?.is_sales || user?.is_admin) {
          const employeeMap = new Map<
            string,
            { hours: number; logs: number }
          >();
          allLogs.forEach((log) => {
            const employeeName =
              log.employee?.employee_name || `员工${log.employee_id}`;
            const current = employeeMap.get(employeeName) || {
              hours: 0,
              logs: 0,
            };
            employeeMap.set(employeeName, {
              hours: current.hours + Number(log.work_hours),
              logs: current.logs + 1,
            });
          });

          employeeStats = Array.from(employeeMap.entries())
            .map(([employee, data]) => ({ employee, ...data }))
            .sort((a, b) => b.hours - a.hours)
            .slice(0, 10);
        }

        setReportData({
          totalStats,
          monthlyStats,
          projectStats,
          employeeStats,
          recentLogs: allLogs.slice(0, 10),
        });
      } catch (error) {
        console.error("获取报表数据失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [
    dateRange,
    selectedEmployee,
    selectedProject,
    user?.is_sales,
    user?.is_admin,
  ]);

  const exportReport = () => {
    // 简单的CSV导出功能
    const csvContent = [
      ["日期", "员工", "项目", "工时", "工作内容"],
      ...reportData.recentLogs.map((log) => [
        log.work_date,
        log.employee?.employee_name || "",
        log.project?.project_name || "",
        log.work_hours.toString(),
        log.work_content.replace(/,/g, "；"), // 替换逗号避免CSV格式问题
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8;",
    });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `工时报表_${dateRange.start}_${dateRange.end}.csv`;
    link.click();
  };

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-300 rounded w-1/4"></div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-64 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">统计报表</h1>
          <p className="mt-2 text-sm text-gray-600">查看工时统计和数据分析</p>
        </div>
        <button
          onClick={exportReport}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:-translate-y-0.5"
        >
          <ArrowDownTrayIcon className="mr-2 h-4 w-4" />
          导出报表
        </button>
      </div>

      {/* 筛选器 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              开始日期
            </label>
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={dateRange.start}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, start: e.target.value }))
              }
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              结束日期
            </label>
            <input
              type="date"
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={dateRange.end}
              onChange={(e) =>
                setDateRange((prev) => ({ ...prev, end: e.target.value }))
              }
            />
          </div>

          {(user?.is_sales || user?.is_admin) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                员工
              </label>
              <select
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={selectedEmployee}
                onChange={(e) => setSelectedEmployee(e.target.value)}
              >
                <option value="">全部员工</option>
                {employees.map((employee) => (
                  <option key={employee.id} value={employee.id.toString()}>
                    {employee.employee_name}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目
            </label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
            >
              <option value="">全部项目</option>
              {projects.map((project) => (
                <option
                  key={project.project_number}
                  value={project.project_number}
                >
                  {project.project_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-blue-50">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总工时</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatHours(reportData.totalStats.total_hours)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-green-50">
                  <DocumentChartBarIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">日志数量</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reportData.totalStats.total_logs}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-purple-50">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">平均工时</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatHours(reportData.totalStats.avg_hours)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-yellow-50">
                  <CalendarIcon className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">工作天数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    new Set(reportData.recentLogs.map((log) => log.work_date))
                      .size
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* 月度工时趋势 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            月度工时趋势
          </h3>
          <div className="space-y-3">
            {reportData.monthlyStats.map((item, index) => (
              <div
                key={item.month}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-gray-700">
                    {item.month}
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">
                    {item.logs} 条日志
                  </span>
                  <span className="text-sm font-semibold text-gray-900">
                    {formatHours(item.hours)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 项目工时分布 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            项目工时分布
          </h3>
          <div className="space-y-3">
            {reportData.projectStats.map((item, index) => (
              <div
                key={item.project}
                className="flex items-center justify-between"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: `hsl(${index * 45}, 70%, 50%)` }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700 truncate max-w-32">
                    {item.project}
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">{item.logs} 条</span>
                  <span className="text-sm font-semibold text-gray-900">
                    {formatHours(item.hours)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 员工工时排行（销售人员和管理员可见） */}
        {(user?.is_sales || user?.is_admin) &&
          reportData.employeeStats.length > 0 && (
            <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                员工工时排行
              </h3>
              <div className="space-y-3">
                {reportData.employeeStats.map((item, index) => (
                  <div
                    key={item.employee}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-bold">
                        {index + 1}
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        {item.employee}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-500">
                        {item.logs} 条
                      </span>
                      <span className="text-sm font-semibold text-gray-900">
                        {formatHours(item.hours)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

        {/* 最近工作记录 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            最近工作记录
          </h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {reportData.recentLogs.map((log) => (
              <div
                key={log.id}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-xs font-medium text-blue-600">
                      {log.project?.project_name}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatHours(log.work_hours)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 line-clamp-2">
                    {log.work_content}
                  </p>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-gray-500">
                      {log.employee?.employee_name}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatDate(log.work_date)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
