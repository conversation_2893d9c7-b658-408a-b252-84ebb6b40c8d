"""项目模型"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class Project(Base):
    """项目信息表"""
    __tablename__ = "project"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    project_number = Column(String(50), nullable=False, unique=True, index=True, comment="项目号")
    project_type = Column(String(100), nullable=False, comment="项目类型")
    customer_abbreviation = Column(String(100), nullable=False, comment="客户简称")
    customer_name = Column(String(200), nullable=False, comment="客户名称")
    project_name = Column(String(200), nullable=False, comment="项目名称")
    employee_id = Column(
        Integer,
        ForeignKey("employee.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
        comment="负责员工ID"
    )
    delete_flag = Column(<PERSON><PERSON>an, nullable=False, default=False, comment="删除标记：0删除，1正常")
    remarks = Column(String(500), nullable=True, comment="备注")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        DateTime, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )
    
    # 关联关系
    employee = relationship("Employee", back_populates="managed_projects")
    work_logs = relationship("WorkLog", back_populates="project")
    
    def __repr__(self):
        return f"<Project(id={self.id}, number='{self.project_number}', name='{self.project_name}')>"
