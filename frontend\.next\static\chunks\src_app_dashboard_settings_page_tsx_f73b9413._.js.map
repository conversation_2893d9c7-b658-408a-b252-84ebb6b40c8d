{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/settings/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  Cog6ToothIcon,\n  UserIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n  BellIcon,\n  EyeIcon,\n  KeyIcon,\n  ClockIcon,\n  DocumentTextIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLog<PERSON>pi, projectApi, employeeApi, departmentApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\n\ninterface SystemStats {\n  totalUsers: number;\n  totalProjects: number;\n  totalDepartments: number;\n  totalWorkLogs: number;\n  systemUptime: string;\n  lastBackup: string;\n}\n\nexport default function SettingsPage() {\n  const { user, canAccessSettings } = useAuth();\n\n  // 权限检查\n  if (!canAccessSettings()) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <ShieldCheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">访问受限</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">您没有权限访问系统设置</p>\n        </div>\n      </div>\n    );\n  }\n  const [stats, setStats] = useState<SystemStats>({\n    totalUsers: 0,\n    totalProjects: 0,\n    totalDepartments: 0,\n    totalWorkLogs: 0,\n    systemUptime: \"0天\",\n    lastBackup: \"未知\",\n  });\n  const [isLoading, setIsLoading] = useState(true);\n  const [isExporting, setIsExporting] = useState(false);\n\n  // 导出数据功能\n  const handleExportData = async (\n    type: \"employees\" | \"projects\" | \"departments\" | \"work_logs\"\n  ) => {\n    try {\n      setIsExporting(true);\n      let data: any[] = [];\n      let filename = \"\";\n\n      switch (type) {\n        case \"employees\":\n          data = await employeeApi.getAll({ limit: 1000 });\n          filename = \"employees.csv\";\n          break;\n        case \"projects\":\n          data = await projectApi.getAll({ limit: 1000 });\n          filename = \"projects.csv\";\n          break;\n        case \"departments\":\n          data = await departmentApi.getAll();\n          filename = \"departments.csv\";\n          break;\n        case \"work_logs\":\n          data = await workLogApi.getAll({ limit: 1000 });\n          filename = \"work_logs.csv\";\n          break;\n      }\n\n      // 转换为CSV格式\n      if (data.length > 0) {\n        const headers = Object.keys(data[0]).join(\",\");\n        const rows = data.map((item) => Object.values(item).join(\",\"));\n        const csv = [headers, ...rows].join(\"\\n\");\n\n        // 下载文件\n        const blob = new Blob([csv], { type: \"text/csv;charset=utf-8;\" });\n        const link = document.createElement(\"a\");\n        link.href = URL.createObjectURL(blob);\n        link.download = filename;\n        link.click();\n      }\n    } catch (error) {\n      console.error(\"导出数据失败:\", error);\n    } finally {\n      setIsExporting(false);\n    }\n  };\n\n  useEffect(() => {\n    const fetchSystemStats = async () => {\n      try {\n        setIsLoading(true);\n        const [employees, projects, departments, workLogs] = await Promise.all([\n          employeeApi.getAll({ limit: 1000 }),\n          projectApi.getAll({ limit: 1000 }),\n          departmentApi.getAll(),\n          workLogApi.getAll({ limit: 1000 }),\n        ]);\n\n        setStats({\n          totalUsers: employees.length,\n          totalProjects: projects.length,\n          totalDepartments: departments.length,\n          totalWorkLogs: workLogs.length,\n          systemUptime: \"7天\",\n          lastBackup: formatDate(new Date()),\n        });\n      } catch (error) {\n        console.error(\"获取系统统计失败:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (user?.is_sales || user?.is_admin) {\n      fetchSystemStats();\n    } else {\n      setIsLoading(false);\n    }\n  }, [user]);\n\n  if (!user?.is_sales && !user?.is_admin) {\n    return (\n      <div className=\"min-h-96 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <ShieldCheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">权限不足</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            只有销售人员或管理员可以访问系统设置\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"grid grid-cols-1 gap-4 lg:grid-cols-2\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"h-64 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">系统设置</h1>\n        <p className=\"mt-2 text-sm text-gray-600\">管理系统配置和查看系统状态</p>\n      </div>\n\n      {/* 系统统计 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <UserIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总用户数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.totalUsers}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <DocumentTextIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">项目数量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.totalProjects}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <ClockIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">工作日志</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.totalWorkLogs}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-yellow-50\">\n                  <CircleStackIcon className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">部门数量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {stats.totalDepartments}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 设置面板 */}\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n        {/* 用户管理 */}\n        <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"p-2 bg-blue-100 rounded-lg mr-3\">\n              <UserIcon className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">用户管理</h3>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">\n                  用户权限管理\n                </p>\n                <p className=\"text-xs text-gray-500\">管理用户角色和权限</p>\n              </div>\n              <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                管理\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">密码策略</p>\n                <p className=\"text-xs text-gray-500\">设置密码复杂度要求</p>\n              </div>\n              <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                配置\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">登录日志</p>\n                <p className=\"text-xs text-gray-500\">查看用户登录记录</p>\n              </div>\n              <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                查看\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 系统配置 */}\n        <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"p-2 bg-green-100 rounded-lg mr-3\">\n              <Cog6ToothIcon className=\"h-6 w-6 text-green-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">系统配置</h3>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">系统参数</p>\n                <p className=\"text-xs text-gray-500\">配置系统基本参数</p>\n              </div>\n              <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                设置\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">邮件配置</p>\n                <p className=\"text-xs text-gray-500\">配置邮件服务器</p>\n              </div>\n              <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                配置\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">备份设置</p>\n                <p className=\"text-xs text-gray-500\">配置自动备份策略</p>\n              </div>\n              <button className=\"text-green-600 hover:text-green-800 text-sm font-medium\">\n                配置\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 安全设置 */}\n        <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"p-2 bg-red-100 rounded-lg mr-3\">\n              <ShieldCheckIcon className=\"h-6 w-6 text-red-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">安全设置</h3>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">访问控制</p>\n                <p className=\"text-xs text-gray-500\">配置IP白名单和访问限制</p>\n              </div>\n              <button className=\"text-red-600 hover:text-red-800 text-sm font-medium\">\n                配置\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">会话管理</p>\n                <p className=\"text-xs text-gray-500\">管理用户会话和超时</p>\n              </div>\n              <button className=\"text-red-600 hover:text-red-800 text-sm font-medium\">\n                管理\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">审计日志</p>\n                <p className=\"text-xs text-gray-500\">查看系统操作记录</p>\n              </div>\n              <button className=\"text-red-600 hover:text-red-800 text-sm font-medium\">\n                查看\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 数据导出 */}\n        <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"p-2 bg-purple-100 rounded-lg mr-3\">\n              <DocumentTextIcon className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">数据导出</h3>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">员工数据</p>\n                <p className=\"text-xs text-gray-500\">导出所有员工信息</p>\n              </div>\n              <button\n                onClick={() => handleExportData(\"employees\")}\n                disabled={isExporting}\n                className=\"text-purple-600 hover:text-purple-800 text-sm font-medium disabled:opacity-50\"\n              >\n                {isExporting ? \"导出中...\" : \"导出\"}\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">项目数据</p>\n                <p className=\"text-xs text-gray-500\">导出所有项目信息</p>\n              </div>\n              <button\n                onClick={() => handleExportData(\"projects\")}\n                disabled={isExporting}\n                className=\"text-purple-600 hover:text-purple-800 text-sm font-medium disabled:opacity-50\"\n              >\n                {isExporting ? \"导出中...\" : \"导出\"}\n              </button>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">工作日志</p>\n                <p className=\"text-xs text-gray-500\">导出所有工作日志</p>\n              </div>\n              <button\n                onClick={() => handleExportData(\"work_logs\")}\n                disabled={isExporting}\n                className=\"text-purple-600 hover:text-purple-800 text-sm font-medium disabled:opacity-50\"\n              >\n                {isExporting ? \"导出中...\" : \"导出\"}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 系统状态 */}\n        <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"p-2 bg-purple-100 rounded-lg mr-3\">\n              <EyeIcon className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">系统状态</h3>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">\n                  系统运行时间\n                </p>\n                <p className=\"text-xs text-gray-500\">{stats.systemUptime}</p>\n              </div>\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                正常\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">\n                  最后备份时间\n                </p>\n                <p className=\"text-xs text-gray-500\">{stats.lastBackup}</p>\n              </div>\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                已完成\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-900\">数据库状态</p>\n                <p className=\"text-xs text-gray-500\">SQLite 数据库</p>\n              </div>\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                连接正常\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 快速操作 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">快速操作</h3>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200\">\n            <CircleStackIcon className=\"h-5 w-5 text-gray-600 mr-2\" />\n            <span className=\"text-sm font-medium text-gray-700\">数据备份</span>\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200\">\n            <KeyIcon className=\"h-5 w-5 text-gray-600 mr-2\" />\n            <span className=\"text-sm font-medium text-gray-700\">重置密钥</span>\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200\">\n            <BellIcon className=\"h-5 w-5 text-gray-600 mr-2\" />\n            <span className=\"text-sm font-medium text-gray-700\">系统通知</span>\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200\">\n            <EyeIcon className=\"h-5 w-5 text-gray-600 mr-2\" />\n            <span className=\"text-sm font-medium text-gray-700\">系统日志</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;;;AAhBA;;;;;;AA2Be,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE1C,OAAO;IACP,IAAI,CAAC,qBAAqB;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gOAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC9C,YAAY;QACZ,eAAe;QACf,kBAAkB;QAClB,eAAe;QACf,cAAc;QACd,YAAY;IACd;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,SAAS;IACT,MAAM,mBAAmB,OACvB;QAEA,IAAI;YACF,eAAe;YACf,IAAI,OAAc,EAAE;YACpB,IAAI,WAAW;YAEf,OAAQ;gBACN,KAAK;oBACH,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBAAE,OAAO;oBAAK;oBAC9C,WAAW;oBACX;gBACF,KAAK;oBACH,OAAO,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;wBAAE,OAAO;oBAAK;oBAC7C,WAAW;oBACX;gBACF,KAAK;oBACH,OAAO,MAAM,oHAAA,CAAA,gBAAa,CAAC,MAAM;oBACjC,WAAW;oBACX;gBACF,KAAK;oBACH,OAAO,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;wBAAE,OAAO;oBAAK;oBAC7C,WAAW;oBACX;YACJ;YAEA,WAAW;YACX,IAAI,KAAK,MAAM,GAAG,GAAG;gBACnB,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;gBAC1C,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,OAAS,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC;gBACzD,MAAM,MAAM;oBAAC;uBAAY;iBAAK,CAAC,IAAI,CAAC;gBAEpC,OAAO;gBACP,MAAM,OAAO,IAAI,KAAK;oBAAC;iBAAI,EAAE;oBAAE,MAAM;gBAA0B;gBAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;gBAChC,KAAK,QAAQ,GAAG;gBAChB,KAAK,KAAK;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;2DAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,MAAM,CAAC,WAAW,UAAU,aAAa,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACrE,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gCAAE,OAAO;4BAAK;4BACjC,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;gCAAE,OAAO;4BAAK;4BAChC,oHAAA,CAAA,gBAAa,CAAC,MAAM;4BACpB,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;gCAAE,OAAO;4BAAK;yBACjC;wBAED,SAAS;4BACP,YAAY,UAAU,MAAM;4BAC5B,eAAe,SAAS,MAAM;4BAC9B,kBAAkB,YAAY,MAAM;4BACpC,eAAe,SAAS,MAAM;4BAC9B,cAAc;4BACd,YAAY,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI;wBAC7B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,CAAA,iBAAA,2BAAA,KAAM,QAAQ,MAAI,iBAAA,2BAAA,KAAM,QAAQ,GAAE;gBACpC;YACF,OAAO;gBACL,aAAa;YACf;QACF;iCAAG;QAAC;KAAK;IAET,IAAI,EAAC,iBAAA,2BAAA,KAAM,QAAQ,KAAI,EAAC,iBAAA,2BAAA,KAAM,QAAQ,GAAE;QACtC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gOAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAMlD;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;IAKpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,MAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAwD;;;;;;;;;;;;kDAI5E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAwD;;;;;;;;;;;;kDAI5E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAwD;;;;;;;;;;;;;;;;;;;;;;;;kCAQhF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAA0D;;;;;;;;;;;;kDAI9E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAA0D;;;;;;;;;;;;kDAI9E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;kCAQlF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAsD;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAsD;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAO,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;kDAE9B,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,UAAU;gDACV,WAAU;0DAET,cAAc,WAAW;;;;;;;;;;;;kDAG9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,UAAU;gDACV,WAAU;0DAET,cAAc,WAAW;;;;;;;;;;;;kDAG9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,UAAU;gDACV,WAAU;0DAET,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAyB,MAAM,YAAY;;;;;;;;;;;;0DAE1D,6LAAC;gDAAK,WAAU;0DAAsG;;;;;;;;;;;;kDAIxH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAyB,MAAM,UAAU;;;;;;;;;;;;0DAExD,6LAAC;gDAAK,WAAU;0DAAsG;;;;;;;;;;;;kDAIxH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAsG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9H,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,gOAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;GA9cwB;;QACc,0HAAA,CAAA,UAAO;;;KADrB", "debugId": null}}]}