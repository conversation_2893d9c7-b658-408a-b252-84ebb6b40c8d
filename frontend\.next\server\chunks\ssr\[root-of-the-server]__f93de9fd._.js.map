{"version": 3, "sources": [], "sections": [{"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from \"axios\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport type {\n  LoginRequest,\n  Token,\n  User,\n  Department,\n  DepartmentCreate,\n  DepartmentUpdate,\n  Employee,\n  EmployeeCreate,\n  EmployeeUpdate,\n  Project,\n  ProjectCreate,\n  ProjectUpdate,\n  WorkLog,\n  WorkLogCreate,\n  WorkLogUpdate,\n  WorkLogStatistics,\n  WorkLogQueryParams,\n} from \"@/types\";\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: `${API_BASE_URL}/api/v1`,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器 - 添加认证 token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = Cookies.get(\"access_token\");\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // 未授权，清除 token 并跳转到登录页\n      Cookies.remove(\"access_token\");\n      window.location.href = \"/login\";\n    } else if (error.response?.data?.detail) {\n      // 显示服务器错误信息\n      toast.error(error.response.data.detail);\n    } else {\n      // 显示通用错误信息\n      toast.error(\"请求失败，请稍后重试\");\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 认证 API\nexport const authApi = {\n  login: async (data: LoginRequest): Promise<Token> => {\n    const response = await apiClient.post<Token>(\"/auth/login\", data);\n    return response.data;\n  },\n\n  getCurrentUser: async (): Promise<User> => {\n    const response = await apiClient.get<User>(\"/employees/me\");\n    return response.data;\n  },\n\n  changePassword: async (data: {\n    current_password: string;\n    new_password: string;\n  }): Promise<{ message: string }> => {\n    const response = await apiClient.post<{ message: string }>(\n      \"/auth/change-password\",\n      data\n    );\n    return response.data;\n  },\n};\n\n// 部门 API\nexport const departmentApi = {\n  getAll: async (): Promise<Department[]> => {\n    const response = await apiClient.get<Department[]>(\"/departments/\");\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Department> => {\n    const response = await apiClient.get<Department>(`/departments/${id}`);\n    return response.data;\n  },\n\n  create: async (data: DepartmentCreate): Promise<Department> => {\n    const response = await apiClient.post<Department>(\"/departments/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: DepartmentUpdate): Promise<Department> => {\n    const response = await apiClient.put<Department>(\n      `/departments/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/departments/${id}`);\n  },\n};\n\n// 员工 API\nexport const employeeApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    department_id?: number;\n  }): Promise<Employee[]> => {\n    const response = await apiClient.get<Employee[]>(\"/employees/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Employee> => {\n    const response = await apiClient.get<Employee>(`/employees/${id}`);\n    return response.data;\n  },\n\n  create: async (data: EmployeeCreate): Promise<Employee> => {\n    const response = await apiClient.post<Employee>(\"/employees/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: EmployeeUpdate): Promise<Employee> => {\n    const response = await apiClient.put<Employee>(`/employees/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/employees/${id}`);\n  },\n};\n\n// 项目 API\nexport const projectApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    manager_id?: number;\n    project_type?: string;\n  }): Promise<Project[]> => {\n    const response = await apiClient.get<Project[]>(\"/projects/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Project> => {\n    const response = await apiClient.get<Project>(`/projects/${id}`);\n    return response.data;\n  },\n\n  getByNumber: async (projectNumber: string): Promise<Project> => {\n    const response = await apiClient.get<Project>(\n      `/projects/by-number/${projectNumber}`\n    );\n    return response.data;\n  },\n\n  create: async (data: ProjectCreate): Promise<Project> => {\n    const response = await apiClient.post<Project>(\"/projects/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: ProjectUpdate): Promise<Project> => {\n    const response = await apiClient.put<Project>(`/projects/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/projects/${id}`);\n  },\n};\n\n// 工作日志 API\nexport const workLogApi = {\n  getAll: async (params?: WorkLogQueryParams): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/\", { params });\n    return response.data;\n  },\n\n  getMy: async (\n    params?: Omit<WorkLogQueryParams, \"employee_id\">\n  ): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/my\", {\n      params,\n    });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<WorkLog> => {\n    const response = await apiClient.get<WorkLog>(`/work-logs/${id}`);\n    return response.data;\n  },\n\n  getStatistics: async (\n    params?: Omit<WorkLogQueryParams, \"skip\" | \"limit\">\n  ): Promise<WorkLogStatistics> => {\n    const response = await apiClient.get<WorkLogStatistics>(\n      \"/work-logs/statistics\",\n      { params }\n    );\n    return response.data;\n  },\n\n  create: async (data: WorkLogCreate): Promise<WorkLog> => {\n    const response = await apiClient.post<WorkLog>(\"/work-logs/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: WorkLogUpdate): Promise<WorkLog> => {\n    const response = await apiClient.put<WorkLog>(`/work-logs/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/work-logs/${id}`);\n  },\n};\n\nexport default apiClient;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAqBA,WAAW;AACX,MAAM,eAAe,6DAAmC;AAExD,cAAc;AACd,MAAM,YAA2B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS,GAAG,aAAa,OAAO,CAAC;IACjC,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qBAAqB;AACrB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,uBAAuB;QACvB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;QACvC,YAAY;QACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;IACxC,OAAO;QACL,WAAW;QACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;IACd;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,UAAU,IAAI,CAAQ,eAAe;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAO;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QAIrB,MAAM,WAAW,MAAM,UAAU,IAAI,CACnC,yBACA;QAEF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ;QACN,MAAM,WAAW,MAAM,UAAU,GAAG,CAAe;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,CAAC,aAAa,EAAE,IAAI;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAa,iBAAiB;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,CAAC,aAAa,EAAE,IAAI,EACpB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC7C;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,OAAO;QAKb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,eAAe;YAAE;QAAO;QACzE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAW,eAAe;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QAMb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,cAAc;YAAE;QAAO;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,CAAC,oBAAoB,EAAE,eAAe;QAExC,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC1C;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,eAAe;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OACL;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,iBAAiB;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,WAAW,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe,OACb;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,yBACA;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,eAAe;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,WAAW,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport { authApi } from \"@/lib/api\";\nimport type { User, LoginRequest } from \"@/types\";\n\ninterface UseAuthReturn {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n  hasPermission: (permission: string) => boolean;\n  canManageEmployees: () => boolean;\n  canManageProjects: () => boolean;\n  canManageDepartments: () => boolean;\n  canAccessSettings: () => boolean;\n  canViewAllLogs: () => boolean;\n}\n\nexport function useAuth(): UseAuthReturn {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  const isAuthenticated = !!user;\n\n  // 获取当前用户信息\n  const fetchUser = useCallback(async () => {\n    try {\n      const token = Cookies.get(\"access_token\");\n      if (!token) {\n        setIsLoading(false);\n        return;\n      }\n\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"获取用户信息失败:\", error);\n      Cookies.remove(\"access_token\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // 登录\n  const login = useCallback(\n    async (credentials: LoginRequest) => {\n      try {\n        setIsLoading(true);\n        const tokenData = await authApi.login(credentials);\n\n        // 保存 token\n        Cookies.set(\"access_token\", tokenData.access_token, {\n          expires: 1, // 1天过期\n          secure: process.env.NODE_ENV === \"production\",\n          sameSite: \"strict\",\n        });\n\n        // 获取用户信息\n        const userData = await authApi.getCurrentUser();\n        setUser(userData);\n\n        toast.success(\"登录成功\");\n        router.push(\"/dashboard\");\n      } catch (error: any) {\n        console.error(\"登录失败:\", error);\n        const message =\n          error.response?.data?.detail || \"登录失败，请检查用户名和密码\";\n        toast.error(message);\n        throw error;\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [router]\n  );\n\n  // 登出\n  const logout = useCallback(() => {\n    Cookies.remove(\"access_token\");\n    setUser(null);\n    toast.success(\"已退出登录\");\n    router.push(\"/login\");\n  }, [router]);\n\n  // 刷新用户信息\n  const refreshUser = useCallback(async () => {\n    try {\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"刷新用户信息失败:\", error);\n    }\n  }, []);\n\n  // 权限检查函数\n  const hasPermission = useCallback(\n    (permission: string) => {\n      if (!user) return false;\n\n      // 管理员（admin）具有所有权限\n      if (user.is_admin) return true;\n\n      switch (permission) {\n        case \"manage_employees\":\n          return user.is_admin; // 只有管理员可以管理员工\n        case \"manage_projects\":\n          return true; // 所有员工都可以管理项目\n        case \"manage_departments\":\n          return user.is_sales || user.is_admin; // 销售人员和管理员可以管理部门\n        case \"access_settings\":\n          return user.is_admin; // 只有管理员可以访问系统设置\n        case \"view_all_logs\":\n          return user.is_admin; // 只有管理员可以查看所有日志\n        default:\n          return false;\n      }\n    },\n    [user]\n  );\n\n  const canManageEmployees = useCallback(\n    () => hasPermission(\"manage_employees\"),\n    [hasPermission]\n  );\n  const canManageProjects = useCallback(\n    () => hasPermission(\"manage_projects\"),\n    [hasPermission]\n  );\n  const canManageDepartments = useCallback(\n    () => hasPermission(\"manage_departments\"),\n    [hasPermission]\n  );\n  const canAccessSettings = useCallback(\n    () => hasPermission(\"access_settings\"),\n    [hasPermission]\n  );\n  const canViewAllLogs = useCallback(\n    () => hasPermission(\"view_all_logs\"),\n    [hasPermission]\n  );\n\n  // 初始化时检查登录状态\n  useEffect(() => {\n    fetchUser();\n  }, [fetchUser]);\n\n  return {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    logout,\n    refreshUser,\n    hasPermission,\n    canManageEmployees,\n    canManageProjects,\n    canManageDepartments,\n    canAccessSettings,\n    canViewAllLogs,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAwBO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC,CAAC;IAE1B,WAAW;IACX,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI;YACF,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,OAAO;gBACV,aAAa;gBACb;YACF;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjB,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,KAAK;IACL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtB,OAAO;QACL,IAAI;YACF,aAAa;YACb,MAAM,YAAY,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAEtC,WAAW;YACX,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,YAAY,EAAE;gBAClD,SAAS;gBACT,QAAQ,oDAAyB;gBACjC,UAAU;YACZ;YAEA,SAAS;YACT,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;YAER,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;YAClC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF,GACA;QAAC;KAAO;IAGV,KAAK;IACL,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,QAAQ;QACR,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACd,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,IAAI,CAAC,MAAM,OAAO;QAElB,mBAAmB;QACnB,IAAI,KAAK,QAAQ,EAAE,OAAO;QAE1B,OAAQ;YACN,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,cAAc;YACtC,KAAK;gBACH,OAAO,MAAM,cAAc;YAC7B,KAAK;gBACH,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,iBAAiB;YAC1D,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;YACxC,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;YACxC;gBACE,OAAO;QACX;IACF,GACA;QAAC;KAAK;IAGR,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,IAAM,cAAc,qBACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,IAAM,cAAc,oBACpB;QAAC;KAAc;IAEjB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,IAAM,cAAc,uBACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,IAAM,cAAc,oBACpB;QAAC;KAAc;IAEjB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,IAAM,cAAc,kBACpB;QAAC;KAAc;IAGjB,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { format, parseISO } from \"date-fns\";\nimport { zhCN } from \"date-fns/locale\";\n\n/**\n * 合并 CSS 类名\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * 格式化日期\n */\nexport function formatDate(\n  date: string | Date,\n  formatStr: string = \"yyyy-MM-dd\"\n): string {\n  const dateObj = typeof date === \"string\" ? parseISO(date) : date;\n  return format(dateObj, formatStr, { locale: zhCN });\n}\n\n/**\n * 格式化日期时间\n */\nexport function formatDateTime(date: string | Date): string {\n  return formatDate(date, \"yyyy-MM-dd HH:mm:ss\");\n}\n\n/**\n * 格式化工时\n */\nexport function formatHours(hours: number | string | null | undefined): string {\n  const numHours = Number(hours) || 0;\n  return `${numHours.toFixed(2)}小时`;\n}\n\n/**\n * 获取今天的日期字符串\n */\nexport function getTodayString(): string {\n  return format(new Date(), \"yyyy-MM-dd\");\n}\n\n/**\n * 验证是否为有效的日期字符串\n */\nexport function isValidDate(dateString: string): boolean {\n  const date = new Date(dateString);\n  return date instanceof Date && !isNaN(date.getTime());\n}\n\n/**\n * 截断文本\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + \"...\";\n}\n\n/**\n * 防抖函数\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * 节流函数\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\n/**\n * 深拷贝对象\n */\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== \"object\") return obj;\n  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;\n  if (obj instanceof Array)\n    return obj.map((item) => deepClone(item)) as unknown as T;\n  if (typeof obj === \"object\") {\n    const clonedObj = {} as T;\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key]);\n      }\n    }\n    return clonedObj;\n  }\n  return obj;\n}\n\n/**\n * 生成随机 ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * 检查是否为空值\n */\nexport function isEmpty(value: any): boolean {\n  if (value === null || value === undefined) return true;\n  if (typeof value === \"string\") return value.trim() === \"\";\n  if (Array.isArray(value)) return value.length === 0;\n  if (typeof value === \"object\") return Object.keys(value).length === 0;\n  return false;\n}\n\n/**\n * 格式化文件大小\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n\n/**\n * 获取文件扩展名\n */\nexport function getFileExtension(filename: string): string {\n  return filename.slice(((filename.lastIndexOf(\".\") - 1) >>> 0) + 2);\n}\n\n/**\n * 验证邮箱格式\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * 验证手机号格式\n */\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^1[3-9]\\d{9}$/;\n  return phoneRegex.test(phone);\n}\n\n/**\n * 获取查询参数\n */\nexport function getQueryParams(url: string): Record<string, string> {\n  const params: Record<string, string> = {};\n  const urlObj = new URL(url);\n  urlObj.searchParams.forEach((value, key) => {\n    params[key] = value;\n  });\n  return params;\n}\n\n/**\n * 构建查询字符串\n */\nexport function buildQueryString(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams();\n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== \"\") {\n      searchParams.append(key, String(value));\n    }\n  });\n  return searchParams.toString();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,WACd,IAAmB,EACnB,YAAoB,YAAY;IAEhC,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,WAAW;QAAE,QAAQ,iJAAA,CAAA,OAAI;IAAC;AACnD;AAKO,SAAS,eAAe,IAAmB;IAChD,OAAO,WAAW,MAAM;AAC1B;AAKO,SAAS,YAAY,KAAyC;IACnE,MAAM,WAAW,OAAO,UAAU;IAClC,OAAO,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,CAAC;AACnC;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;AAC5B;AAKO,SAAS,YAAY,UAAkB;IAC5C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,gBAAgB,QAAQ,CAAC,MAAM,KAAK,OAAO;AACpD;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAKO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,eAAe,OACjB,OAAO,IAAI,GAAG,CAAC,CAAC,OAAS,UAAU;IACrC,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,IAAI,cAAc,CAAC,MAAM;gBAC3B,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;YACrC;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAKO,SAAS,QAAQ,KAAU;IAChC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,OAAO,UAAU,UAAU,OAAO,MAAM,IAAI,OAAO;IACvD,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,MAAM,MAAM,KAAK;IAClD,IAAI,OAAO,UAAU,UAAU,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK;IACpE,OAAO;AACT;AAKO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,AAAC,SAAS,WAAW,CAAC,OAAO,MAAO,CAAC,IAAI;AAClE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,eAAe,GAAW;IACxC,MAAM,SAAiC,CAAC;IACxC,MAAM,SAAS,IAAI,IAAI;IACvB,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO;QAClC,MAAM,CAAC,IAAI,GAAG;IAChB;IACA,OAAO;AACT;AAKO,SAAS,iBAAiB,MAA2B;IAC1D,MAAM,eAAe,IAAI;IACzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,aAAa,MAAM,CAAC,KAAK,OAAO;QAClC;IACF;IACA,OAAO,aAAa,QAAQ;AAC9B", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/ChangePasswordModal.tsx"], "sourcesContent": ["'use client';\n\nimport { Fragment } from 'react';\nimport { Dialog, Transition } from '@headlessui/react';\nimport { XMarkIcon, KeyIcon } from '@heroicons/react/24/outline';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport toast from 'react-hot-toast';\nimport { authApi } from '@/lib/api';\n\nconst changePasswordSchema = z.object({\n  current_password: z.string().min(1, '请输入当前密码'),\n  new_password: z.string().min(6, '新密码至少6位').max(50, '新密码不能超过50位'),\n  confirm_password: z.string().min(1, '请确认新密码'),\n}).refine((data) => data.new_password === data.confirm_password, {\n  message: \"两次输入的密码不一致\",\n  path: [\"confirm_password\"],\n});\n\ninterface ChangePasswordFormData {\n  current_password: string;\n  new_password: string;\n  confirm_password: string;\n}\n\ninterface ChangePasswordModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function ChangePasswordModal({ isOpen, onClose }: ChangePasswordModalProps) {\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n  } = useForm<ChangePasswordFormData>({\n    resolver: zodResolver(changePasswordSchema),\n  });\n\n  const onSubmit = async (data: ChangePasswordFormData) => {\n    try {\n      await authApi.changePassword({\n        current_password: data.current_password,\n        new_password: data.new_password,\n      });\n      toast.success('密码修改成功');\n      reset();\n      onClose();\n    } catch (error: any) {\n      console.error('修改密码失败:', error);\n      const message = error.response?.data?.detail || '修改密码失败';\n      toast.error(message);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <KeyIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title as=\"h3\" className=\"text-lg font-semibold text-gray-900\">\n                        修改密码\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">更改您的登录密码</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      当前密码 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"password\"\n                      {...register('current_password')}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.current_password ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入当前密码\"\n                    />\n                    {errors.current_password && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.current_password.message}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      新密码 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"password\"\n                      {...register('new_password')}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.new_password ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请输入新密码（至少6位）\"\n                    />\n                    {errors.new_password && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.new_password.message}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      确认新密码 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"password\"\n                      {...register('confirm_password')}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.confirm_password ? 'border-red-300' : 'border-gray-300'\n                      }`}\n                      placeholder=\"请再次输入新密码\"\n                    />\n                    {errors.confirm_password && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.confirm_password.message}</p>\n                    )}\n                  </div>\n\n                  <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-3\">\n                    <div className=\"flex\">\n                      <div className=\"ml-3\">\n                        <h3 className=\"text-sm font-medium text-yellow-800\">\n                          密码安全提示\n                        </h3>\n                        <div className=\"mt-2 text-sm text-yellow-700\">\n                          <ul className=\"list-disc list-inside space-y-1\">\n                            <li>密码长度至少6位</li>\n                            <li>建议包含字母、数字和特殊字符</li>\n                            <li>不要使用过于简单的密码</li>\n                          </ul>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                          </svg>\n                          修改中...\n                        </div>\n                      ) : (\n                        '确认修改'\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,uBAAuB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,kBAAkB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,IAAI;IACnD,kBAAkB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACtC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,YAAY,KAAK,KAAK,gBAAgB,EAAE;IAC/D,SAAS;IACT,MAAM;QAAC;KAAmB;AAC5B;AAae,SAAS,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAA4B;IACvF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA0B;QAClC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc,CAAC;gBAC3B,kBAAkB,KAAK,gBAAgB;gBACvC,cAAc,KAAK,YAAY;YACjC;YACA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEAAC,IAAG;gEAAK,WAAU;0EAAsC;;;;;;0EAGtE,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACzD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,mBAAmB;wDAChC,WAAW,CAAC,wIAAwI,EAClJ,OAAO,gBAAgB,GAAG,mBAAmB,mBAC7C;wDACF,aAAY;;;;;;oDAEb,OAAO,gBAAgB,kBACtB,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;0DAI7E,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EAC1D,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAErC,8OAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,eAAe;wDAC5B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,YAAY,GAAG,mBAAmB,mBACzC;wDACF,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAIzE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACxD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEvC,8OAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,mBAAmB;wDAChC,WAAW,CAAC,wIAAwI,EAClJ,OAAO,gBAAgB,GAAG,mBAAmB,mBAC7C;wDACF,aAAY;;;;;;oDAEb,OAAO,gBAAgB,kBACtB,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;0DAI7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EAGpD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;sFACJ,8OAAC;sFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA6C,MAAK;oEAAO,SAAQ;;sFAC9E,8OAAC;4EAAO,WAAU;4EAAa,IAAG;4EAAK,IAAG;4EAAK,GAAE;4EAAK,QAAO;4EAAe,aAAY;;;;;;sFACxF,8OAAC;4EAAK,WAAU;4EAAa,MAAK;4EAAe,GAAE;;;;;;;;;;;;gEAC/C;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport {\n  HomeIcon,\n  ClipboardDocumentListIcon,\n  UserGroupIcon,\n  BuildingOfficeIcon,\n  FolderIcon,\n  ChartBarIcon,\n  Cog6ToothIcon,\n  ArrowLeftOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  KeyIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { cn } from \"@/lib/utils\";\nimport ChangePasswordModal from \"@/components/modals/ChangePasswordModal\";\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  adminOnly?: boolean;\n  salesOnly?: boolean;\n  permission?: string;\n}\n\nconst navigation: NavigationItem[] = [\n  { name: \"仪表板\", href: \"/dashboard\", icon: HomeIcon },\n  {\n    name: \"我的工时\",\n    href: \"/dashboard/work-logs\",\n    icon: ClipboardDocumentListIcon,\n  },\n  { name: \"项目管理\", href: \"/dashboard/projects\", icon: FolderIcon },\n  {\n    name: \"员工管理\",\n    href: \"/dashboard/employees\",\n    icon: UserGroupIcon,\n    permission: \"manage_employees\",\n  },\n  {\n    name: \"部门管理\",\n    href: \"/dashboard/departments\",\n    icon: BuildingOfficeIcon,\n    permission: \"manage_departments\",\n  },\n  { name: \"统计报表\", href: \"/dashboard/reports\", icon: ChartBarIcon },\n  {\n    name: \"系统设置\",\n    href: \"/dashboard/settings\",\n    icon: Cog6ToothIcon,\n    permission: \"access_settings\",\n  },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport default function Sidebar({ className }: SidebarProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n  const { user, logout, hasPermission } = useAuth();\n  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);\n\n  const filteredNavigation = navigation.filter((item) => {\n    // 如果有权限要求，检查权限\n    if (item.permission) {\n      return hasPermission(item.permission);\n    }\n    // 兼容旧的 adminOnly 属性\n    if (item.adminOnly) {\n      return user?.is_sales || user?.is_admin;\n    }\n    // 默认显示\n    return true;\n  });\n\n  const SidebarContent = () => (\n    <div className=\"flex flex-col h-full\">\n      {/* Logo */}\n      <div className=\"flex items-center justify-center h-20 px-4 bg-gradient-to-r from-blue-600 to-purple-700\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"h-10 w-10 bg-white/20 rounded-xl flex items-center justify-center\">\n            <svg\n              className=\"h-6 w-6 text-white\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n              />\n            </svg>\n          </div>\n          <div>\n            <h1 className=\"text-white text-lg font-bold\">工时管理</h1>\n            <p className=\"text-blue-100 text-xs\">Management System</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n        {filteredNavigation.map((item) => {\n          const isActive = pathname === item.href;\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                \"group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 transform hover:scale-105\",\n                isActive\n                  ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg\"\n                  : \"text-gray-600 hover:bg-gray-100 hover:text-gray-900\"\n              )}\n              onClick={() => setIsMobileMenuOpen(false)}\n            >\n              <item.icon\n                className={cn(\n                  \"mr-3 h-5 w-5 flex-shrink-0\",\n                  isActive\n                    ? \"text-white\"\n                    : \"text-gray-400 group-hover:text-gray-600\"\n                )}\n              />\n              <span className=\"truncate\">{item.name}</span>\n              {isActive && (\n                <div className=\"ml-auto\">\n                  <div className=\"h-2 w-2 bg-white rounded-full\"></div>\n                </div>\n              )}\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* User info and logout */}\n      <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n        <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 mb-3\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg\">\n                <span className=\"text-lg font-bold text-white\">\n                  {user?.employee_name?.charAt(0)}\n                </span>\n              </div>\n            </div>\n            <div className=\"ml-3 flex-1 min-w-0\">\n              <p className=\"text-sm font-semibold text-gray-900 truncate\">\n                {user?.employee_name}\n              </p>\n              <p className=\"text-xs text-gray-500 truncate\">\n                {user?.department?.department_name}\n              </p>\n              <div className=\"flex items-center mt-1\">\n                <div className=\"h-2 w-2 bg-green-400 rounded-full mr-1\"></div>\n                <span className=\"text-xs text-green-600 font-medium\">在线</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <button\n          onClick={() => setShowChangePasswordModal(true)}\n          className=\"w-full flex items-center px-4 py-3 text-sm font-medium text-gray-600 rounded-xl hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 group mb-2\"\n        >\n          <KeyIcon className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-blue-500\" />\n          <span>修改密码</span>\n        </button>\n\n        <button\n          onClick={logout}\n          className=\"w-full flex items-center px-4 py-3 text-sm font-medium text-gray-600 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 group\"\n        >\n          <ArrowLeftOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400 group-hover:text-red-500\" />\n          <span>退出登录</span>\n          <svg\n            className=\"ml-auto h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M9 5l7 7-7 7\"\n            />\n          </svg>\n        </button>\n      </div>\n    </div>\n  );\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-0 left-0 z-50 p-4\">\n        <button\n          type=\"button\"\n          className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n          onClick={() => setIsMobileMenuOpen(true)}\n        >\n          <Bars3Icon className=\"h-6 w-6\" />\n        </button>\n      </div>\n\n      {/* Mobile menu overlay */}\n      {isMobileMenuOpen && (\n        <div className=\"lg:hidden fixed inset-0 z-50 flex\">\n          <div\n            className=\"fixed inset-0 bg-gray-600 bg-opacity-75\"\n            onClick={() => setIsMobileMenuOpen(false)}\n          />\n          <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n            <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n              <button\n                type=\"button\"\n                className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n                onClick={() => setIsMobileMenuOpen(false)}\n              >\n                <XMarkIcon className=\"h-6 w-6 text-white\" />\n              </button>\n            </div>\n            <SidebarContent />\n          </div>\n        </div>\n      )}\n\n      {/* Desktop sidebar */}\n      <div\n        className={cn(\n          \"hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 bg-white border-r border-gray-200\",\n          className\n        )}\n      >\n        <SidebarContent />\n      </div>\n\n      {/* 修改密码模态框 */}\n      <ChangePasswordModal\n        isOpen={showChangePasswordModal}\n        onClose={() => setShowChangePasswordModal(false)}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AApBA;;;;;;;;;AA+BA,MAAM,aAA+B;IACnC;QAAE,MAAM;QAAO,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAClD;QACE,MAAM;QACN,MAAM;QACN,MAAM,iPAAA,CAAA,4BAAyB;IACjC;IACA;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,mNAAA,CAAA,aAAU;IAAC;IAC9D;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAA,CAAA,gBAAa;QACnB,YAAY;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mOAAA,CAAA,qBAAkB;QACxB,YAAY;IACd;IACA;QAAE,MAAM;QAAQ,MAAM;QAAsB,MAAM,uNAAA,CAAA,eAAY;IAAC;IAC/D;QACE,MAAM;QACN,MAAM;QACN,MAAM,yNAAA,CAAA,gBAAa;QACnB,YAAY;IACd;CACD;AAMc,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC;QAC5C,eAAe;QACf,IAAI,KAAK,UAAU,EAAE;YACnB,OAAO,cAAc,KAAK,UAAU;QACtC;QACA,oBAAoB;QACpB,IAAI,KAAK,SAAS,EAAE;YAClB,OAAO,MAAM,YAAY,MAAM;QACjC;QACA,OAAO;QACP,OAAO;IACT;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAM3C,8OAAC;oBAAI,WAAU;8BACZ,mBAAmB,GAAG,CAAC,CAAC;wBACvB,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA,WACI,sEACA;4BAEN,SAAS,IAAM,oBAAoB;;8CAEnC,8OAAC,KAAK,IAAI;oCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WACI,eACA;;;;;;8CAGR,8OAAC;oCAAK,WAAU;8CAAY,KAAK,IAAI;;;;;;gCACpC,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BArBd,KAAK,IAAI;;;;;oBA0BpB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,MAAM,eAAe,OAAO;;;;;;;;;;;;;;;;kDAInC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DACV,MAAM,YAAY;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK7D,8OAAC;4BACC,SAAS,IAAM,2BAA2B;4BAC1C,WAAU;;8CAEV,8OAAC,6MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;sCAGR,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,+OAAA,CAAA,2BAAwB;oCAAC,WAAU;;;;;;8CACpC,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQd,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,oBAAoB;8BAEnC,cAAA,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKxB,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,oBAAoB;;;;;;kCAErC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAEnC,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,8OAAC;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;0BAGF,cAAA,8OAAC;;;;;;;;;;0BAIH,8OAAC,mJAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,2BAA2B;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\nimport Sidebar from '@/components/Sidebar';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gray-100\">\n      <Sidebar />\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden lg:ml-64\">\n        <main className=\"flex-1 relative overflow-y-auto focus:outline-none\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}