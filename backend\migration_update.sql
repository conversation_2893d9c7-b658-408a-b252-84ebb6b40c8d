-- 数据库结构更新脚本
-- 添加逻辑删除字段和修改项目表字段名

-- 1. 为项目表添加逻辑删除字段和重命名字段
ALTER TABLE project ADD COLUMN delete_flag BOOLEAN NOT NULL DEFAULT FALSE COMMENT '删除标记：0删除，1正常';
ALTER TABLE project CHANGE COLUMN manager_id employee_id INT NOT NULL COMMENT '负责员工ID';

-- 2. 为员工表添加逻辑删除字段
ALTER TABLE employee ADD COLUMN delete_flag BOOLEAN NOT NULL DEFAULT FALSE COMMENT '删除标记：0删除，1正常';

-- 3. 为部门表添加逻辑删除字段
ALTER TABLE department ADD COLUMN delete_flag BOOLEAN NOT NULL DEFAULT FALSE COMMENT '删除标记：0删除，1正常';

-- 4. 更新现有数据，将所有记录的删除标记设为正常（1）
UPDATE project SET delete_flag = FALSE WHERE delete_flag IS NULL;
UPDATE employee SET delete_flag = FALSE WHERE delete_flag IS NULL;
UPDATE department SET delete_flag = FALSE WHERE delete_flag IS NULL;

-- 5. 添加索引以提高查询性能
CREATE INDEX idx_project_delete_flag ON project(delete_flag);
CREATE INDEX idx_employee_delete_flag ON employee(delete_flag);
CREATE INDEX idx_department_delete_flag ON department(delete_flag);

-- 6. 更新外键约束名称（如果需要）
-- ALTER TABLE project DROP FOREIGN KEY project_ibfk_1;
-- ALTER TABLE project ADD CONSTRAINT fk_project_employee FOREIGN KEY (employee_id) REFERENCES employee(id) ON DELETE RESTRICT;

COMMIT;
