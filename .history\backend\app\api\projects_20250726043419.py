"""项目管理 API"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload

from ..auth import get_current_employee
from ..database import get_db
from ..models.employee import Employee
from ..models.project import Project
from ..schemas.project import Project as ProjectSchema, ProjectCreate, ProjectUpdate

router = APIRouter(prefix="/projects", tags=["项目管理"])


@router.get("/", response_model=List[ProjectSchema], summary="获取项目列表")
async def get_projects(
    skip: int = 0,
    limit: int = 100,
    employee_id: int = None,
    project_type: str = None,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取项目列表"""
    query = db.query(Project).options(joinedload(Project.employee)).filter(Project.delete_flag == False)

    if employee_id:
        query = query.filter(Project.employee_id == employee_id)

    if project_type:
        query = query.filter(Project.project_type == project_type)

    projects = query.offset(skip).limit(limit).all()
    return projects


@router.get("/{project_id}", response_model=ProjectSchema, summary="获取项目详情")
async def get_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取项目详情"""
    project = db.query(Project).options(joinedload(Project.manager)).filter(
        Project.id == project_id
    ).first()
    if project is None:
        raise HTTPException(status_code=404, detail="项目不存在")
    return project


@router.get("/by-number/{project_number}", response_model=ProjectSchema, summary="根据项目号获取项目")
async def get_project_by_number(
    project_number: str,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """根据项目号获取项目"""
    project = db.query(Project).options(joinedload(Project.manager)).filter(
        Project.project_number == project_number
    ).first()
    if project is None:
        raise HTTPException(status_code=404, detail="项目不存在")
    return project


@router.post("/", response_model=ProjectSchema, summary="创建项目")
async def create_project(
    project: ProjectCreate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """创建项目"""
    # 检查项目号是否已存在
    existing_project = db.query(Project).filter(Project.project_number == project.project_number).first()
    if existing_project:
        raise HTTPException(status_code=400, detail="项目号已存在")
    
    # 检查业务经理是否存在
    manager = db.query(Employee).filter(Employee.id == project.manager_id).first()
    if not manager:
        raise HTTPException(status_code=400, detail="业务经理不存在")
    
    db_project = Project(**project.model_dump())
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    
    # 重新查询以包含关联数据
    project_with_manager = db.query(Project).options(joinedload(Project.manager)).filter(
        Project.id == db_project.id
    ).first()
    
    return project_with_manager


@router.put("/{project_id}", response_model=ProjectSchema, summary="更新项目")
async def update_project(
    project_id: int,
    project: ProjectUpdate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """更新项目"""
    db_project = db.query(Project).filter(Project.id == project_id).first()
    if db_project is None:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    update_data = project.model_dump(exclude_unset=True)
    
    # 检查项目号是否已存在（如果要更新项目号）
    if "project_number" in update_data:
        existing_project = db.query(Project).filter(
            Project.project_number == update_data["project_number"],
            Project.id != project_id
        ).first()
        if existing_project:
            raise HTTPException(status_code=400, detail="项目号已存在")
    
    # 检查业务经理是否存在（如果要更新业务经理）
    if "manager_id" in update_data:
        manager = db.query(Employee).filter(Employee.id == update_data["manager_id"]).first()
        if not manager:
            raise HTTPException(status_code=400, detail="业务经理不存在")
    
    for field, value in update_data.items():
        setattr(db_project, field, value)
    
    db.commit()
    db.refresh(db_project)
    
    # 重新查询以包含关联数据
    project_with_manager = db.query(Project).options(joinedload(Project.manager)).filter(
        Project.id == db_project.id
    ).first()
    
    return project_with_manager


@router.delete("/{project_id}", summary="删除项目")
async def delete_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """删除项目"""
    db_project = db.query(Project).filter(Project.id == project_id).first()
    if db_project is None:
        raise HTTPException(status_code=404, detail="项目不存在")
    
    db.delete(db_project)
    db.commit()
    return {"message": "项目删除成功"}
