"use client";

import { useState, useEffect } from "react";
import {
  ClipboardDocumentListIcon,
  FolderIcon,
  UserGroupIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { workLog<PERSON>pi, projectApi, employeeApi } from "@/lib/api";
import { formatHours, formatDate, getTodayString } from "@/lib/utils";
import CreateWorkLogModal from "@/components/modals/CreateWorkLogModal";
import type { WorkLogStatistics, WorkLog } from "@/types";

interface DashboardStats {
  totalHours: number;
  totalLogs: number;
  totalProjects: number;
  totalEmployees: number;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalHours: 0,
    totalLogs: 0,
    totalProjects: 0,
    totalEmployees: 0,
  });
  const [recentLogs, setRecentLogs] = useState<WorkLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // 获取工时统计
        const workLogStats = await workLogApi.getStatistics({
          start_date: new Date(
            new Date().getFullYear(),
            new Date().getMonth(),
            1
          )
            .toISOString()
            .split("T")[0],
          end_date: getTodayString(),
        });

        // 获取最近的工作日志（管理员可以查看所有，普通用户只能查看自己的）
        const logs = user?.is_admin
          ? await workLogApi.getAll({ limit: 10 })
          : await workLogApi.getMy({ limit: 5 });

        // 如果是销售人员，获取更多统计信息
        let projectCount = 0;
        let employeeCount = 0;

        if (user?.is_sales) {
          const [projects, employees] = await Promise.all([
            projectApi.getAll({ limit: 1000 }),
            employeeApi.getAll({ limit: 1000 }),
          ]);
          projectCount = projects.length;
          employeeCount = employees.length;
        }

        setStats({
          totalHours: workLogStats.total_hours,
          totalLogs: workLogStats.total_logs,
          totalProjects: projectCount,
          totalEmployees: employeeCount,
        });
        setRecentLogs(logs);
      } catch (error) {
        console.error("获取仪表板数据失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const statCards = [
    {
      name: "本月工时",
      value: formatHours(stats.totalHours),
      icon: ClockIcon,
      gradient: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600",
      change: "+12%",
      changeType: "increase",
    },
    {
      name: "工作日志",
      value: stats.totalLogs.toString(),
      icon: ClipboardDocumentListIcon,
      gradient: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600",
      change: "+8%",
      changeType: "increase",
    },
    ...(user?.is_sales
      ? [
          {
            name: "项目数量",
            value: stats.totalProjects.toString(),
            icon: FolderIcon,
            gradient: "from-yellow-500 to-yellow-600",
            bgColor: "bg-yellow-50",
            iconColor: "text-yellow-600",
            change: "+3%",
            changeType: "increase",
          },
          {
            name: "员工数量",
            value: stats.totalEmployees.toString(),
            icon: UserGroupIcon,
            gradient: "from-purple-500 to-purple-600",
            bgColor: "bg-purple-50",
            iconColor: "text-purple-600",
            change: "+2%",
            changeType: "increase",
          },
        ]
      : []),
  ];

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-gray-300 rounded"></div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 欢迎信息 */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">
              欢迎回来，{user?.employee_name}！
            </h1>
            <p className="mt-2 text-blue-100">
              今天是 {formatDate(new Date(), "yyyy年MM月dd日")}，祝您工作愉快！
            </p>
            <div className="mt-4 flex items-center space-x-4">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-400 rounded-full mr-2"></div>
                <span className="text-sm">系统运行正常</span>
              </div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-yellow-400 rounded-full mr-2"></div>
                <span className="text-sm">
                  {user?.department?.department_name}
                </span>
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="h-24 w-24 bg-white/20 rounded-full flex items-center justify-center">
              <svg
                className="h-12 w-12 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((item) => (
          <div
            key={item.name}
            className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          >
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-xl ${item.bgColor}`}>
                      <item.icon className={`h-6 w-6 ${item.iconColor}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600 truncate">
                        {item.name}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {item.value}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        item.changeType === "increase"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {item.changeType === "increase" ? (
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      {item.change}
                    </span>
                    <span className="ml-2 text-xs text-gray-500">vs 上月</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 最近的工作日志 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100">
        <div className="px-6 py-5 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-900">
              最近的工作日志
            </h3>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              最新 {recentLogs.length} 条
            </span>
          </div>
        </div>
        <div className="p-6">
          {recentLogs.length > 0 ? (
            <div className="space-y-4">
              {recentLogs.map((log) => (
                <div
                  key={log.id}
                  className="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                        <ClipboardDocumentListIcon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="text-sm font-semibold text-gray-900 truncate">
                              {log.project?.project_name || "未知项目"}
                            </h4>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {formatHours(log.work_hours)}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-xs text-gray-500">客户:</span>
                            <span className="text-xs font-medium text-gray-700 truncate">
                              {log.project?.customer_name || "未知客户"}
                            </span>
                            {user?.is_admin && (
                              <>
                                <span className="text-xs text-gray-400">•</span>
                                <span className="text-xs text-gray-500">
                                  员工:
                                </span>
                                <span className="text-xs font-medium text-gray-700">
                                  {log.employee?.employee_name || "未知员工"}
                                </span>
                              </>
                            )}
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2 mb-2">
                            {log.work_content}
                          </p>
                          {log.remarks && (
                            <p className="text-xs text-gray-500 italic">
                              备注: {log.remarks}
                            </p>
                          )}
                        </div>
                        <div className="flex-shrink-0 ml-4">
                          <div className="text-right">
                            <div className="text-sm font-medium text-gray-900">
                              {formatDate(log.work_date)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(log.created_at).toLocaleTimeString(
                                "zh-CN",
                                {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                }
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <div className="text-center pt-4">
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                  查看全部日志
                  <svg
                    className="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center">
                <ClipboardDocumentListIcon className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                暂无工作日志
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                开始记录您的第一条工作日志吧！
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  创建工作日志
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 新建工作日志模态框 */}
      <CreateWorkLogModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          // 刷新数据
          window.location.reload();
        }}
      />
    </div>
  );
}
