{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/CreateProjectModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport { XMarkIcon, FolderIcon } from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { projectApi, employeeApi } from \"@/lib/api\";\nimport type { Employee } from \"@/types\";\n\nconst projectSchema = z.object({\n  project_number: z\n    .string()\n    .min(1, \"请输入项目号\")\n    .max(50, \"项目号不能超过50字符\"),\n  project_type: z\n    .string()\n    .min(1, \"请输入项目类型\")\n    .max(100, \"项目类型不能超过100字符\"),\n  customer_abbreviation: z\n    .string()\n    .min(1, \"请输入客户简称\")\n    .max(100, \"客户简称不能超过100字符\"),\n  customer_name: z\n    .string()\n    .min(1, \"请输入客户名称\")\n    .max(200, \"客户名称不能超过200字符\"),\n  project_name: z\n    .string()\n    .min(1, \"请输入项目名称\")\n    .max(200, \"项目名称不能超过200字符\"),\n  manager_id: z.number().min(1, \"请选择销售经理\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface ProjectFormData {\n  project_number: string;\n  project_type: string;\n  customer_abbreviation: string;\n  customer_name: string;\n  project_name: string;\n  manager_id: number;\n  remarks?: string;\n}\n\ninterface CreateProjectModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function CreateProjectModal({\n  isOpen,\n  onClose,\n  onSuccess,\n}: CreateProjectModalProps) {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ProjectFormData>({\n    resolver: zodResolver(projectSchema),\n    defaultValues: {\n      project_number: \"\",\n      project_type: \"\",\n      customer_abbreviation: \"\",\n      customer_name: \"\",\n      project_name: \"\",\n      manager_id: 0,\n      remarks: \"\",\n    },\n  });\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setIsLoading(true);\n        const employeesData = await employeeApi.getAll({ limit: 100 });\n        setEmployees(employeesData.filter((emp) => emp.is_sales));\n      } catch (error) {\n        console.error(\"获取员工列表失败:\", error);\n        toast.error(\"获取员工列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchEmployees();\n    }\n  }, [isOpen]);\n\n  const onSubmit = async (data: ProjectFormData) => {\n    try {\n      setIsSubmitting(true);\n      await projectApi.create(data);\n\n      toast.success(\"项目创建成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"创建项目失败:\", error);\n      const message = error.response?.data?.detail || \"创建项目失败\";\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        新建项目\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">创建新的项目信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 项目号 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目号 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_number\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_number\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"PRJ001\"\n                      />\n                      {errors.project_number && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_number.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 项目类型 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目类型 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_type\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_type\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"Web开发\"\n                      />\n                      {errors.project_type && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_type.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 客户简称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户简称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_abbreviation\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_abbreviation\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC公司\"\n                      />\n                      {errors.customer_abbreviation && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_abbreviation.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 客户名称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户名称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_name\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_name\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC科技有限公司\"\n                      />\n                      {errors.customer_name && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_name.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 项目名称 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目名称 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      {...register(\"project_name\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_name\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      placeholder=\"企业官网建设项目\"\n                    />\n                    {errors.project_name && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.project_name.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 销售经理 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      销售经理 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"manager_id\", { valueAsNumber: true })}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.manager_id ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value={0}>请选择销售经理</option>\n                      {employees.map((employee) => (\n                        <option key={employee.id} value={employee.id}>\n                          {employee.employee_name} -{\" \"}\n                          {employee.department?.department_name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.manager_id && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.manager_id.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={3}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的项目备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          创建中...\n                        </div>\n                      ) : (\n                        \"创建项目\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYA,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,gBAAgB,6KAAA,CAAA,IAAC,CACd,MAAM,GACN,GAAG,CAAC,GAAG,UACP,GAAG,CAAC,IAAI;IACX,cAAc,6KAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,uBAAuB,6KAAA,CAAA,IAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,eAAe,6KAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,cAAc,6KAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAkBe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACe;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,gBAAgB;YAChB,cAAc;YACd,uBAAuB;YACvB,eAAe;YACf,cAAc;YACd,YAAY;YACZ,SAAS;QACX;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,aAAa;gBACb,MAAM,gBAAgB,MAAM,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAC5D,aAAa,cAAc,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ;YACzD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAChB,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YAExB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFAC1D,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAErC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,iBAAiB;gEAC9B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,cAAc,GACjB,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,cAAc,kBACpB,8OAAC;gEAAE,WAAU;0EACV,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;kEAMpC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,eAAe;gEAC5B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,YAAY,GACf,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,YAAY,kBAClB,8OAAC;gEAAE,WAAU;0EACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,wBAAwB;gEACrC,WAAW,CAAC,wIAAwI,EAClJ,OAAO,qBAAqB,GACxB,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,qBAAqB,kBAC3B,8OAAC;gEAAE,WAAU;0EACV,OAAO,qBAAqB,CAAC,OAAO;;;;;;;;;;;;kEAM3C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,gBAAgB;gEAC7B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,aAAa,GAChB,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,aAAa,kBACnB,8OAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAOrC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACzD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,eAAe;wDAC5B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,YAAY,GACf,mBACA,mBACJ;wDACF,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,8OAAC;wDAAE,WAAU;kEACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAMlC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACzD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDACE,GAAG,SAAS,cAAc;4DAAE,eAAe;wDAAK,EAAE;wDACnD,WAAW,CAAC,wIAAwI,EAClJ,OAAO,UAAU,GAAG,mBAAmB,mBACvC;wDACF,UAAU;;0EAEV,8OAAC;gEAAO,OAAO;0EAAG;;;;;;4DACjB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;oEAAyB,OAAO,SAAS,EAAE;;wEACzC,SAAS,aAAa;wEAAC;wEAAG;wEAC1B,SAAS,UAAU,EAAE;;mEAFX,SAAS,EAAE;;;;;;;;;;;oDAM3B,OAAO,UAAU,kBAChB,8OAAC;wDAAE,WAAU;kEACV,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;0DAMhC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,8OAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,8OAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/projects/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  FolderIcon,\n  UserIcon,\n  CalendarIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n} from \"@heroicons/react/24/outline\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { projectApi, employeeApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\nimport CreateProjectModal from \"@/components/modals/CreateProjectModal\";\nimport EditProjectModal from \"@/components/modals/EditProjectModal\";\nimport ViewProjectModal from \"@/components/modals/ViewProjectModal\";\nimport type { Project, Employee } from \"@/types\";\n\nexport default function ProjectsPage() {\n  const { user } = useAuth();\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedType, setSelectedType] = useState(\"\");\n  const [selectedManager, setSelectedManager] = useState(\"\");\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [projectsData, employeesData] = await Promise.all([\n        projectApi.getAll({ limit: 100 }),\n        employeeApi.getAll({ limit: 100 }),\n      ]);\n      setProjects(projectsData);\n      setEmployees(employeesData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const handleView = (project: Project) => {\n    setSelectedProject(project);\n    setShowViewModal(true);\n  };\n\n  const handleEdit = (project: Project) => {\n    setSelectedProject(project);\n    setShowEditModal(true);\n  };\n\n  const handleDelete = async (project: Project) => {\n    if (window.confirm(`确定要删除项目 \"${project.project_name}\" 吗？`)) {\n      try {\n        await projectApi.delete(project.id);\n        toast.success(\"项目删除成功\");\n        fetchData();\n      } catch (error: any) {\n        console.error(\"删除项目失败:\", error);\n        const message = error.response?.data?.detail || \"删除项目失败\";\n        toast.error(message);\n      }\n    }\n  };\n\n  const filteredProjects = projects.filter((project) => {\n    const matchesSearch =\n      project.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      project.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      project.project_number.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = !selectedType || project.project_type === selectedType;\n    const matchesManager =\n      !selectedManager || project.manager_id.toString() === selectedManager;\n\n    return matchesSearch && matchesType && matchesManager;\n  });\n\n  const projectTypes = [...new Set(projects.map((p) => p.project_type))];\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">项目管理</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">管理和跟踪所有项目信息</p>\n        </div>\n        {(user?.is_sales || user?.is_admin) && (\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\"\n          >\n            <PlusIcon className=\"mr-2 h-4 w-4\" />\n            新建项目\n          </button>\n        )}\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-3\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总项目数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredProjects.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <UserIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">销售经理</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {new Set(projects.map((p) => p.manager_id)).size}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <CalendarIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">项目类型</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {projectTypes.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 筛选器 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              搜索\n            </label>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                placeholder=\"搜索项目名称、客户或项目号...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              项目类型\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedType}\n              onChange={(e) => setSelectedType(e.target.value)}\n            >\n              <option value=\"\">全部类型</option>\n              {projectTypes.map((type) => (\n                <option key={type} value={type}>\n                  {type}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              销售经理\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedManager}\n              onChange={(e) => setSelectedManager(e.target.value)}\n            >\n              <option value=\"\">全部经理</option>\n              {employees\n                .filter((emp) => emp.is_sales)\n                .map((employee) => (\n                  <option key={employee.id} value={employee.id.toString()}>\n                    {employee.employee_name}\n                  </option>\n                ))}\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm(\"\");\n                setSelectedType(\"\");\n                setSelectedManager(\"\");\n              }}\n              className=\"w-full px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n            >\n              重置筛选\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 项目列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">项目列表</h3>\n        </div>\n\n        {filteredProjects.length > 0 ? (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    项目信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    客户信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    销售经理\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    创建时间\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredProjects.map((project) => (\n                  <tr\n                    key={project.id}\n                    className=\"hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"flex items-center\">\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2\">\n                            {project.project_number}\n                          </span>\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                            {project.project_type}\n                          </span>\n                        </div>\n                        <div className=\"text-sm font-medium text-gray-900 mt-1\">\n                          {project.project_name}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {project.customer_name}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {project.customer_abbreviation}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {project.manager?.employee_name}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {project.manager?.department?.department_name}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(project.created_at)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          onClick={() => handleView(project)}\n                          className=\"text-blue-600 hover:text-blue-800 transition-colors duration-200\"\n                          title=\"查看详情\"\n                        >\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        {(user?.is_sales || user?.is_admin) && (\n                          <>\n                            <button\n                              onClick={() => handleEdit(project)}\n                              className=\"text-green-600 hover:text-green-800 transition-colors duration-200\"\n                              title=\"编辑项目\"\n                            >\n                              <PencilIcon className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              onClick={() => handleDelete(project)}\n                              className=\"text-red-600 hover:text-red-800 transition-colors duration-200\"\n                              title=\"删除项目\"\n                            >\n                              <TrashIcon className=\"h-4 w-4\" />\n                            </button>\n                          </>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <FolderIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无项目</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedType || selectedManager\n                ? \"没有找到符合条件的项目\"\n                : \"开始创建您的第一个项目吧！\"}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* 新建项目模态框 */}\n      <CreateProjectModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={fetchData}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;AAsBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,cAAc,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAC/B,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;aACjC;YACD,YAAY;YACZ,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,GAAG;YAC1D,IAAI;gBACF,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,WAAW;gBACzB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,gBACJ,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,QAAQ,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,QAAQ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtE,MAAM,cAAc,CAAC,gBAAgB,QAAQ,YAAY,KAAK;QAC9D,MAAM,iBACJ,CAAC,mBAAmB,QAAQ,UAAU,CAAC,QAAQ,OAAO;QAExD,OAAO,iBAAiB,eAAe;IACzC;IAEA,MAAM,eAAe;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY;KAAG;IAEtE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAE3C,CAAC,MAAM,YAAY,MAAM,QAAQ,mBAChC,8OAAC;wBACC,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sDAE/C,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gDAAkB,OAAO;0DACvB;+CADU;;;;;;;;;;;;;;;;;sCAOnB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;sDAElD,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,UACE,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,EAC5B,GAAG,CAAC,CAAC,yBACJ,8OAAC;gDAAyB,OAAO,SAAS,EAAE,CAAC,QAAQ;0DAClD,SAAS,aAAa;+CADZ,SAAS,EAAE;;;;;;;;;;;;;;;;;sCAOhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;oCACP,cAAc;oCACd,gBAAgB;oCAChB,mBAAmB;gCACrB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,8OAAC;oCAAM,WAAU;8CACd,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFACb,QAAQ,cAAc;;;;;;kFAEzB,8OAAC;wEAAK,WAAU;kFACb,QAAQ,YAAY;;;;;;;;;;;;0EAGzB,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,YAAY;;;;;;;;;;;;;;;;;8DAI3B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,aAAa;;;;;;sEAExB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,qBAAqB;;;;;;;;;;;;8DAGlC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO,EAAE;;;;;;sEAEpB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO,EAAE,YAAY;;;;;;;;;;;;8DAGlC,8OAAC;oDAAG,WAAU;8DACX,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;8DAEhC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,WAAW;gEAC1B,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;4DAEpB,CAAC,MAAM,YAAY,MAAM,QAAQ,mBAChC;;kFACE,8OAAC;wEACC,SAAS,IAAM,WAAW;wEAC1B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;kFAExB,8OAAC;wEACC,SAAS,IAAM,aAAa;wEAC5B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CA5D1B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;6CAwEzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,cAAc,gBAAgB,kBAC3B,gBACA;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC,kJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}]}