#!/usr/bin/env python3
"""检查用户数据"""

import sqlite3

def check_users():
    """检查用户数据"""
    conn = sqlite3.connect('work_log_system.db')
    cursor = conn.cursor()
    
    # 查询所有用户
    cursor.execute('SELECT id, employee_name, password, is_admin FROM employee')
    users = cursor.fetchall()
    
    print("数据库中的用户:")
    for user in users:
        print(f"ID: {user[0]}, 用户名: {user[1]}, 密码哈希: {user[2][:50]}..., 管理员: {user[3]}")
    
    conn.close()

if __name__ == "__main__":
    check_users()
