{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/CreateEmployeeModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport { XMarkIcon, UserIcon } from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { employeeApi, departmentApi } from \"@/lib/api\";\nimport type { Department } from \"@/types\";\n\nconst employeeSchema = z.object({\n  employee_name: z\n    .string()\n    .min(1, \"请输入员工姓名\")\n    .max(100, \"员工姓名不能超过100字符\"),\n  password: z.string().min(6, \"密码至少6位\").max(100, \"密码不能超过100字符\"),\n  is_sales: z.boolean(),\n  is_admin: z.boolean(),\n  department_id: z.number().min(1, \"请选择部门\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface EmployeeFormData {\n  employee_name: string;\n  password: string;\n  is_sales: boolean;\n  is_admin: boolean;\n  department_id: number;\n  remarks?: string;\n}\n\ninterface CreateEmployeeModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function CreateEmployeeModal({\n  isOpen,\n  onClose,\n  onSuccess,\n}: CreateEmployeeModalProps) {\n  const [departments, setDepartments] = useState<Department[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n  } = useForm<EmployeeFormData>({\n    resolver: zodResolver(employeeSchema),\n    defaultValues: {\n      employee_name: \"\",\n      password: \"\",\n      is_sales: false,\n      is_admin: false,\n      department_id: 0,\n      remarks: \"\",\n    },\n  });\n\n  useEffect(() => {\n    const fetchDepartments = async () => {\n      try {\n        setIsLoading(true);\n        const departmentsData = await departmentApi.getAll();\n        setDepartments(departmentsData);\n      } catch (error) {\n        console.error(\"获取部门列表失败:\", error);\n        toast.error(\"获取部门列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchDepartments();\n    }\n  }, [isOpen]);\n\n  const onSubmit = async (data: EmployeeFormData) => {\n    try {\n      await employeeApi.create(data);\n      toast.success(\"员工创建成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"创建员工失败:\", error);\n      const message = error.response?.data?.detail || \"创建员工失败\";\n      toast.error(message);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <UserIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        新建员工\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">创建新的员工账号</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 员工姓名 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        员工姓名 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"employee_name\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.employee_name\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"请输入员工姓名\"\n                      />\n                      {errors.employee_name && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.employee_name.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 登录密码 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        登录密码 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"password\"\n                        {...register(\"password\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.password ? \"border-red-300\" : \"border-gray-300\"\n                        }`}\n                        placeholder=\"请输入登录密码\"\n                      />\n                      {errors.password && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.password.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 所属部门 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        所属部门 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <select\n                        {...register(\"department_id\", { valueAsNumber: true })}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.department_id\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        disabled={isLoading}\n                      >\n                        <option value={0}>请选择部门</option>\n                        {departments.map((department) => (\n                          <option key={department.id} value={department.id}>\n                            {department.department_name}\n                          </option>\n                        ))}\n                      </select>\n                      {errors.department_id && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.department_id.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 员工角色 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        员工角色\n                      </label>\n                      <div className=\"flex items-center space-x-4 pt-2\">\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            {...register(\"is_sales\", { valueAsNumber: false })}\n                            value=\"false\"\n                            defaultChecked={true}\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                          />\n                          <span className=\"ml-2 text-sm text-gray-700\">\n                            普通员工\n                          </span>\n                        </label>\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            {...register(\"is_sales\", { valueAsNumber: false })}\n                            value=\"true\"\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                          />\n                          <span className=\"ml-2 text-sm text-gray-700\">\n                            销售人员\n                          </span>\n                        </label>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 管理员权限 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      管理员权限\n                    </label>\n                    <div className=\"flex items-center space-x-4 pt-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          {...register(\"is_admin\", { valueAsNumber: false })}\n                          value=\"false\"\n                          defaultChecked={true}\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">\n                          普通权限\n                        </span>\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          {...register(\"is_admin\", { valueAsNumber: false })}\n                          value=\"true\"\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">\n                          管理员权限\n                        </span>\n                      </label>\n                    </div>\n                    <p className=\"mt-1 text-xs text-gray-500\">\n                      管理员可以查看所有员工的工作日志和管理系统\n                    </p>\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={3}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的员工备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          创建中...\n                        </div>\n                      ) : (\n                        \"创建员工\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYA,MAAM,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,eAAe,6KAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC/C,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO;IACnB,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO;IACnB,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAiBe,SAAS,oBAAoB,EAC1C,MAAM,EACN,OAAO,EACP,SAAS,EACgB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,eAAe;YACf,UAAU;YACV,UAAU;YACV,UAAU;YACV,eAAe;YACf,SAAS;QACX;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,MAAM,kBAAkB,MAAM,iHAAA,CAAA,gBAAa,CAAC,MAAM;gBAClD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACzB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,gBAAgB;gEAC7B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,aAAa,GAChB,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,aAAa,kBACnB,8OAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kEAMnC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,WAAW;gEACxB,WAAW,CAAC,wIAAwI,EAClJ,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;gEACF,aAAY;;;;;;4DAEb,OAAO,QAAQ,kBACd,8OAAC;gEAAE,WAAU;0EACV,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAMhC,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACE,GAAG,SAAS,iBAAiB;oEAAE,eAAe;gEAAK,EAAE;gEACtD,WAAW,CAAC,wIAAwI,EAClJ,OAAO,aAAa,GAChB,mBACA,mBACJ;gEACF,UAAU;;kFAEV,8OAAC;wEAAO,OAAO;kFAAG;;;;;;oEACjB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;4EAA2B,OAAO,WAAW,EAAE;sFAC7C,WAAW,eAAe;2EADhB,WAAW,EAAE;;;;;;;;;;;4DAK7B,OAAO,aAAa,kBACnB,8OAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kEAMnC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,WAAU;;0FACf,8OAAC;gFACC,MAAK;gFACJ,GAAG,SAAS,YAAY;oFAAE,eAAe;gFAAM,EAAE;gFAClD,OAAM;gFACN,gBAAgB;gFAChB,WAAU;;;;;;0FAEZ,8OAAC;gFAAK,WAAU;0FAA6B;;;;;;;;;;;;kFAI/C,8OAAC;wEAAM,WAAU;;0FACf,8OAAC;gFACC,MAAK;gFACJ,GAAG,SAAS,YAAY;oFAAE,eAAe;gFAAM,EAAE;gFAClD,OAAM;gFACN,WAAU;;;;;;0FAEZ,8OAAC;gFAAK,WAAU;0FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASrD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACJ,GAAG,SAAS,YAAY;4EAAE,eAAe;wEAAM,EAAE;wEAClD,OAAM;wEACN,gBAAgB;wEAChB,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAI/C,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACJ,GAAG,SAAS,YAAY;4EAAE,eAAe;wEAAM,EAAE;wEAClD,OAAM;wEACN,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAKjD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAM5C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,8OAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,8OAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/EditEmployeeModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport { XMarkIcon, UserIcon } from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { employeeApi, departmentApi } from \"@/lib/api\";\nimport type { Employee, Department } from \"@/types\";\n\nconst employeeSchema = z.object({\n  employee_name: z\n    .string()\n    .min(1, \"请输入员工姓名\")\n    .max(100, \"员工姓名不能超过100字符\"),\n  password: z\n    .string()\n    .min(6, \"密码至少6位\")\n    .max(100, \"密码不能超过100字符\")\n    .optional(),\n  is_sales: z.boolean(),\n  is_admin: z.boolean(),\n  department_id: z.number().min(1, \"请选择部门\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface EmployeeFormData {\n  employee_name: string;\n  password?: string;\n  is_sales: boolean;\n  is_admin: boolean;\n  department_id: number;\n  remarks?: string;\n}\n\ninterface EditEmployeeModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n  employee: Employee | null;\n}\n\nexport default function EditEmployeeModal({\n  isOpen,\n  onClose,\n  onSuccess,\n  employee,\n}: EditEmployeeModalProps) {\n  const [departments, setDepartments] = useState<Department[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n    setValue,\n    watch,\n  } = useForm<EmployeeFormData>({\n    resolver: zodResolver(employeeSchema),\n  });\n\n  const watchedIsSales = watch(\"is_sales\");\n  const watchedIsAdmin = watch(\"is_admin\");\n\n  useEffect(() => {\n    const fetchDepartments = async () => {\n      try {\n        setIsLoading(true);\n        const departmentsData = await departmentApi.getAll();\n        setDepartments(departmentsData);\n      } catch (error) {\n        console.error(\"获取部门列表失败:\", error);\n        toast.error(\"获取部门列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchDepartments();\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    if (employee && isOpen) {\n      setValue(\"employee_name\", employee.employee_name);\n      setValue(\"is_sales\", employee.is_sales);\n      setValue(\"is_admin\", employee.is_admin);\n      setValue(\"department_id\", employee.department_id);\n      setValue(\"remarks\", employee.remarks || \"\");\n      setValue(\"password\", \"\"); // 密码字段留空\n    }\n  }, [employee, isOpen, setValue]);\n\n  const onSubmit = async (data: EmployeeFormData) => {\n    if (!employee) return;\n\n    try {\n      // 如果密码为空，则不更新密码\n      const updateData = { ...data };\n      if (!data.password) {\n        delete updateData.password;\n      }\n\n      await employeeApi.update(employee.id, updateData);\n      toast.success(\"员工信息更新成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"更新员工信息失败:\", error);\n      const message = error.response?.data?.detail || \"更新员工信息失败\";\n      toast.error(message);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <UserIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        编辑员工\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">修改员工信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 员工姓名 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        员工姓名 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"employee_name\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.employee_name\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"请输入员工姓名\"\n                      />\n                      {errors.employee_name && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.employee_name.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 新密码 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        新密码\n                      </label>\n                      <input\n                        type=\"password\"\n                        {...register(\"password\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.password ? \"border-red-300\" : \"border-gray-300\"\n                        }`}\n                        placeholder=\"留空则不修改密码\"\n                      />\n                      {errors.password && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.password.message}\n                        </p>\n                      )}\n                      <p className=\"mt-1 text-xs text-gray-500\">\n                        留空则不修改密码\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 所属部门 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        所属部门 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <select\n                        {...register(\"department_id\", { valueAsNumber: true })}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.department_id\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        disabled={isLoading}\n                      >\n                        <option value={0}>请选择部门</option>\n                        {departments.map((department) => (\n                          <option key={department.id} value={department.id}>\n                            {department.department_name}\n                          </option>\n                        ))}\n                      </select>\n                      {errors.department_id && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.department_id.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 员工角色 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        员工角色\n                      </label>\n                      <div className=\"flex items-center space-x-4 pt-2\">\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            {...register(\"is_sales\", { valueAsNumber: false })}\n                            value=\"false\"\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                          />\n                          <span className=\"ml-2 text-sm text-gray-700\">\n                            普通员工\n                          </span>\n                        </label>\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            {...register(\"is_sales\", { valueAsNumber: false })}\n                            value=\"true\"\n                            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                          />\n                          <span className=\"ml-2 text-sm text-gray-700\">\n                            销售人员\n                          </span>\n                        </label>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 管理员权限 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      管理员权限\n                    </label>\n                    <div className=\"flex items-center space-x-4 pt-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          {...register(\"is_admin\", { valueAsNumber: false })}\n                          value=\"false\"\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">\n                          普通权限\n                        </span>\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          {...register(\"is_admin\", { valueAsNumber: false })}\n                          value=\"true\"\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                        />\n                        <span className=\"ml-2 text-sm text-gray-700\">\n                          管理员权限\n                        </span>\n                      </label>\n                    </div>\n                    <p className=\"mt-1 text-xs text-gray-500\">\n                      管理员可以查看所有员工的工作日志和管理系统\n                    </p>\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={3}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的员工备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          更新中...\n                        </div>\n                      ) : (\n                        \"更新员工\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYA,MAAM,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,eAAe,6KAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,UAAU,6KAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,UACP,GAAG,CAAC,KAAK,eACT,QAAQ;IACX,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO;IACnB,UAAU,6KAAA,CAAA,IAAC,CAAC,OAAO;IACnB,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAkBe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACe;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACL,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,iBAAiB,MAAM;IAC7B,MAAM,iBAAiB,MAAM;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,MAAM,kBAAkB,MAAM,iHAAA,CAAA,gBAAa,CAAC,MAAM;gBAClD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,QAAQ;YACtB,SAAS,iBAAiB,SAAS,aAAa;YAChD,SAAS,YAAY,SAAS,QAAQ;YACtC,SAAS,YAAY,SAAS,QAAQ;YACtC,SAAS,iBAAiB,SAAS,aAAa;YAChD,SAAS,WAAW,SAAS,OAAO,IAAI;YACxC,SAAS,YAAY,KAAK,SAAS;QACrC;IACF,GAAG;QAAC;QAAU;QAAQ;KAAS;IAE/B,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,gBAAgB;YAChB,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,OAAO,WAAW,QAAQ;YAC5B;YAEA,MAAM,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;YACtC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,gBAAgB;gEAC7B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,aAAa,GAChB,mBACA,mBACJ;gEACF,aAAY;;;;;;4DAEb,OAAO,aAAa,kBACnB,8OAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kEAMnC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,WAAW;gEACxB,WAAW,CAAC,wIAAwI,EAClJ,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;gEACF,aAAY;;;;;;4DAEb,OAAO,QAAQ,kBACd,8OAAC;gEAAE,WAAU;0EACV,OAAO,QAAQ,CAAC,OAAO;;;;;;0EAG5B,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;;;;;;;0DAM9C,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACE,GAAG,SAAS,iBAAiB;oEAAE,eAAe;gEAAK,EAAE;gEACtD,WAAW,CAAC,wIAAwI,EAClJ,OAAO,aAAa,GAChB,mBACA,mBACJ;gEACF,UAAU;;kFAEV,8OAAC;wEAAO,OAAO;kFAAG;;;;;;oEACjB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;4EAA2B,OAAO,WAAW,EAAE;sFAC7C,WAAW,eAAe;2EADhB,WAAW,EAAE;;;;;;;;;;;4DAK7B,OAAO,aAAa,kBACnB,8OAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kEAMnC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,WAAU;;0FACf,8OAAC;gFACC,MAAK;gFACJ,GAAG,SAAS,YAAY;oFAAE,eAAe;gFAAM,EAAE;gFAClD,OAAM;gFACN,WAAU;;;;;;0FAEZ,8OAAC;gFAAK,WAAU;0FAA6B;;;;;;;;;;;;kFAI/C,8OAAC;wEAAM,WAAU;;0FACf,8OAAC;gFACC,MAAK;gFACJ,GAAG,SAAS,YAAY;oFAAE,eAAe;gFAAM,EAAE;gFAClD,OAAM;gFACN,WAAU;;;;;;0FAEZ,8OAAC;gFAAK,WAAU;0FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASrD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACJ,GAAG,SAAS,YAAY;4EAAE,eAAe;wEAAM,EAAE;wEAClD,OAAM;wEACN,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAI/C,8OAAC;gEAAM,WAAU;;kFACf,8OAAC;wEACC,MAAK;wEACJ,GAAG,SAAS,YAAY;4EAAE,eAAe;wEAAM,EAAE;wEAClD,OAAM;wEACN,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAKjD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAM5C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,8OAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,8OAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/ViewEmployeeModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport {\n  XMarkIcon,\n  UserIcon,\n  BuildingOfficeIcon,\n  CalendarIcon,\n  ShieldCheckIcon,\n} from \"@heroicons/react/24/outline\";\nimport { formatDate } from \"@/lib/utils\";\nimport type { Employee } from \"@/types\";\n\ninterface ViewEmployeeModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  employee: Employee | null;\n}\n\nexport default function ViewEmployeeModal({\n  isOpen,\n  onClose,\n  employee,\n}: ViewEmployeeModalProps) {\n  if (!employee) return null;\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <UserIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        员工详情\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">查看员工详细信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={onClose}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-6\">\n                  {/* 基本信息 */}\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <UserIcon className=\"h-4 w-4 mr-2\" />\n                      基本信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          员工姓名\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {employee.employee_name}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          员工ID\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          #{employee.id}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 部门信息 */}\n                  <div className=\"bg-blue-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <BuildingOfficeIcon className=\"h-4 w-4 mr-2\" />\n                      部门信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          所属部门\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {employee.department?.department_name || \"未知部门\"}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          部门ID\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          #{employee.department_id}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 权限信息 */}\n                  <div className=\"bg-green-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <ShieldCheckIcon className=\"h-4 w-4 mr-2\" />\n                      权限信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          员工角色\n                        </label>\n                        <div className=\"mt-1\">\n                          <span\n                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              employee.is_sales\n                                ? \"bg-green-100 text-green-800\"\n                                : \"bg-gray-100 text-gray-800\"\n                            }`}\n                          >\n                            {employee.is_sales ? \"销售人员\" : \"普通员工\"}\n                          </span>\n                        </div>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          管理员权限\n                        </label>\n                        <div className=\"mt-1\">\n                          <span\n                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                              employee.is_admin\n                                ? \"bg-red-100 text-red-800\"\n                                : \"bg-gray-100 text-gray-800\"\n                            }`}\n                          >\n                            {employee.is_admin ? \"管理员\" : \"普通权限\"}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 时间信息 */}\n                  <div className=\"bg-purple-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                      时间信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          入职时间\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {formatDate(employee.created_at)}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          信息更新时间\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {formatDate(employee.updated_at)}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 备注信息 */}\n                  {employee.remarks && (\n                    <div className=\"bg-yellow-50 rounded-lg p-4\">\n                      <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                        <UserIcon className=\"h-4 w-4 mr-2\" />\n                        备注信息\n                      </h4>\n                      <p className=\"text-sm text-gray-700\">\n                        {employee.remarks}\n                      </p>\n                    </div>\n                  )}\n\n                  {/* 权限说明 */}\n                  <div className=\"bg-indigo-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3\">\n                      权限说明\n                    </h4>\n                    <div className=\"space-y-2 text-xs text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-gray-400 rounded-full mr-2\"></span>\n                        <span>\n                          普通员工：只能查看和管理自己的工作日志，可以进行项目管理\n                        </span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                        <span>\n                          销售人员：可以查看自己的日志和相关项目的日志，可以进行项目管理和部门管理\n                        </span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-red-400 rounded-full mr-2\"></span>\n                        <span>\n                          管理员：具有最高权限，可以查看所有员工的工作日志，管理员工和访问系统设置\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 关闭按钮 */}\n                  <div className=\"flex items-center justify-end pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={onClose}\n                    >\n                      关闭\n                    </button>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAXA;;;;;;AAoBe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,QAAQ,EACe;IACvB,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;kFACV,SAAS,aAAa;;;;;;;;;;;;0EAG3B,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;;4EAA6B;4EACtC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0DAOrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,mOAAA,CAAA,qBAAkB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;kFACV,SAAS,UAAU,EAAE,mBAAmB;;;;;;;;;;;;0EAG7C,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;;4EAA6B;4EACtC,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;0DAOhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,6NAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EACC,WAAW,CAAC,wEAAwE,EAClF,SAAS,QAAQ,GACb,gCACA,6BACJ;sFAED,SAAS,QAAQ,GAAG,SAAS;;;;;;;;;;;;;;;;;0EAIpC,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EACC,WAAW,CAAC,wEAAwE,EAClF,SAAS,QAAQ,GACb,4BACA,6BACJ;sFAED,SAAS,QAAQ,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,uNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG3C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;0EAGnC,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,8OAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;4CAOtC,SAAS,OAAO,kBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,8OAAC;wDAAE,WAAU;kEACV,SAAS,OAAO;;;;;;;;;;;;0DAMvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEAGvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;0EAIR,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;0EAIR,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;0DAQZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/employees/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  UserGroupIcon,\n  BuildingOfficeIcon,\n  ShieldCheckIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n} from \"@heroicons/react/24/outline\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { employeeApi, departmentApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\nimport CreateEmployeeModal from \"@/components/modals/CreateEmployeeModal\";\nimport EditEmployeeModal from \"@/components/modals/EditEmployeeModal\";\nimport ViewEmployeeModal from \"@/components/modals/ViewEmployeeModal\";\nimport type { Employee, Department } from \"@/types\";\n\nexport default function EmployeesPage() {\n  const { user } = useAuth();\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [departments, setDepartments] = useState<Department[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedDepartment, setSelectedDepartment] = useState(\"\");\n  const [selectedRole, setSelectedRole] = useState(\"\");\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(\n    null\n  );\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [employeesData, departmentsData] = await Promise.all([\n        employeeApi.getAll({ limit: 100 }),\n        departmentApi.getAll(),\n      ]);\n      setEmployees(employeesData);\n      setDepartments(departmentsData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const handleView = (employee: Employee) => {\n    setSelectedEmployee(employee);\n    setShowViewModal(true);\n  };\n\n  const handleEdit = (employee: Employee) => {\n    setSelectedEmployee(employee);\n    setShowEditModal(true);\n  };\n\n  const handleDelete = async (employee: Employee) => {\n    if (window.confirm(`确定要删除员工 \"${employee.employee_name}\" 吗？`)) {\n      try {\n        await employeeApi.delete(employee.id);\n        toast.success(\"员工删除成功\");\n        fetchData();\n      } catch (error: any) {\n        console.error(\"删除员工失败:\", error);\n        const message = error.response?.data?.detail || \"删除员工失败\";\n        toast.error(message);\n      }\n    }\n  };\n\n  const filteredEmployees = employees.filter((employee) => {\n    const matchesSearch = employee.employee_name\n      .toLowerCase()\n      .includes(searchTerm.toLowerCase());\n    const matchesDepartment =\n      !selectedDepartment ||\n      employee.department_id.toString() === selectedDepartment;\n    const matchesRole =\n      !selectedRole ||\n      (selectedRole === \"sales\" && employee.is_sales) ||\n      (selectedRole === \"regular\" && !employee.is_sales);\n\n    return matchesSearch && matchesDepartment && matchesRole;\n  });\n\n  const salesCount = employees.filter((emp) => emp.is_sales).length;\n  const regularCount = employees.filter((emp) => !emp.is_sales).length;\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">员工管理</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">管理员工信息和权限设置</p>\n        </div>\n        {(user?.is_sales || user?.is_admin) && (\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\"\n          >\n            <PlusIcon className=\"mr-2 h-4 w-4\" />\n            新建员工\n          </button>\n        )}\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-4\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <UserGroupIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总员工数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredEmployees.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <ShieldCheckIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">销售人员</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{salesCount}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <UserIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">普通员工</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {regularCount}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-yellow-50\">\n                  <BuildingOfficeIcon className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">部门数量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {departments.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 筛选器 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              搜索\n            </label>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                placeholder=\"搜索员工姓名...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              部门\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedDepartment}\n              onChange={(e) => setSelectedDepartment(e.target.value)}\n            >\n              <option value=\"\">全部部门</option>\n              {departments.map((department) => (\n                <option key={department.id} value={department.id.toString()}>\n                  {department.department_name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              角色\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedRole}\n              onChange={(e) => setSelectedRole(e.target.value)}\n            >\n              <option value=\"\">全部角色</option>\n              <option value=\"sales\">销售人员</option>\n              <option value=\"regular\">普通员工</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm(\"\");\n                setSelectedDepartment(\"\");\n                setSelectedRole(\"\");\n              }}\n              className=\"w-full px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n            >\n              重置筛选\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 员工列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">员工列表</h3>\n        </div>\n\n        {filteredEmployees.length > 0 ? (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    员工信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    部门\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    角色\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    入职时间\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredEmployees.map((employee) => (\n                  <tr\n                    key={employee.id}\n                    className=\"hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-white\">\n                              {employee.employee_name.charAt(0)}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {employee.employee_name}\n                          </div>\n                          {employee.remarks && (\n                            <div className=\"text-sm text-gray-500\">\n                              {employee.remarks}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {employee.department?.department_name}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex flex-wrap gap-1\">\n                        <span\n                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            employee.is_sales\n                              ? \"bg-green-100 text-green-800\"\n                              : \"bg-gray-100 text-gray-800\"\n                          }`}\n                        >\n                          {employee.is_sales ? \"销售人员\" : \"普通员工\"}\n                        </span>\n                        {employee.is_admin && (\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                            管理员\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(employee.created_at)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          onClick={() => handleView(employee)}\n                          className=\"text-blue-600 hover:text-blue-800 transition-colors duration-200\"\n                          title=\"查看详情\"\n                        >\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        {user?.is_admin && (\n                          <>\n                            <button\n                              onClick={() => handleEdit(employee)}\n                              className=\"text-green-600 hover:text-green-800 transition-colors duration-200\"\n                              title=\"编辑员工\"\n                            >\n                              <PencilIcon className=\"h-4 w-4\" />\n                            </button>\n                            {employee.id !== user.id && (\n                              <button\n                                onClick={() => handleDelete(employee)}\n                                className=\"text-red-600 hover:text-red-800 transition-colors duration-200\"\n                                title=\"删除员工\"\n                              >\n                                <TrashIcon className=\"h-4 w-4\" />\n                              </button>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <UserGroupIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无员工</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedDepartment || selectedRole\n                ? \"没有找到符合条件的员工\"\n                : \"开始添加您的第一个员工吧！\"}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* 新建员工模态框 */}\n      <CreateEmployeeModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={fetchData}\n      />\n\n      {/* 编辑员工模态框 */}\n      <EditEmployeeModal\n        isOpen={showEditModal}\n        onClose={() => setShowEditModal(false)}\n        onSuccess={fetchData}\n        employee={selectedEmployee}\n      />\n\n      {/* 查看员工模态框 */}\n      <ViewEmployeeModal\n        isOpen={showViewModal}\n        onClose={() => setShowViewModal(false)}\n        employee={selectedEmployee}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD;IAGF,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,eAAe,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzD,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAChC,iHAAA,CAAA,gBAAa,CAAC,MAAM;aACrB;YACD,aAAa;YACb,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,oBAAoB;QACpB,iBAAiB;IACnB;IAEA,MAAM,aAAa,CAAC;QAClB,oBAAoB;QACpB,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,aAAa,CAAC,IAAI,CAAC,GAAG;YAC5D,IAAI;gBACF,MAAM,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,SAAS,EAAE;gBACpC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,WAAW;gBACzB,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAC;QAC1C,MAAM,gBAAgB,SAAS,aAAa,CACzC,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW;QAClC,MAAM,oBACJ,CAAC,sBACD,SAAS,aAAa,CAAC,QAAQ,OAAO;QACxC,MAAM,cACJ,CAAC,gBACA,iBAAiB,WAAW,SAAS,QAAQ,IAC7C,iBAAiB,aAAa,CAAC,SAAS,QAAQ;QAEnD,OAAO,iBAAiB,qBAAqB;IAC/C;IAEA,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,EAAE,MAAM;IACjE,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,MAAQ,CAAC,IAAI,QAAQ,EAAE,MAAM;IAEpE,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAE3C,CAAC,MAAM,YAAY,MAAM,QAAQ,mBAChC,8OAAC;wBACC,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6NAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;;sDAErD,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;gDAA2B,OAAO,WAAW,EAAE,CAAC,QAAQ;0DACtD,WAAW,eAAe;+CADhB,WAAW,EAAE;;;;;;;;;;;;;;;;;sCAOhC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sDAE/C,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;oCACP,cAAc;oCACd,sBAAsB;oCACtB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,kBAAkB,MAAM,GAAG,kBAC1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,8OAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,8OAAC;oCAAM,WAAU;8CACd,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFACb,SAAS,aAAa,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;0EAIrC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,SAAS,aAAa;;;;;;oEAExB,SAAS,OAAO,kBACf,8OAAC;wEAAI,WAAU;kFACZ,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;8DAM3B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAU;kEACb,SAAS,UAAU,EAAE;;;;;;;;;;;8DAG1B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAW,CAAC,wEAAwE,EAClF,SAAS,QAAQ,GACb,gCACA,6BACJ;0EAED,SAAS,QAAQ,GAAG,SAAS;;;;;;4DAE/B,SAAS,QAAQ,kBAChB,8OAAC;gEAAK,WAAU;0EAAkG;;;;;;;;;;;;;;;;;8DAMxH,8OAAC;oDAAG,WAAU;8DACX,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;8DAEjC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,WAAW;gEAC1B,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;4DAEpB,MAAM,0BACL;;kFACE,8OAAC;wEACC,SAAS,IAAM,WAAW;wEAC1B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,mNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;oEAEvB,SAAS,EAAE,KAAK,KAAK,EAAE,kBACtB,8OAAC;wEACC,SAAS,IAAM,aAAa;wEAC5B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,iNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CA1E5B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;6CAuF1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,cAAc,sBAAsB,eACjC,gBACA;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC,mJAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,WAAW;;;;;;0BAIb,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,WAAW;gBACX,UAAU;;;;;;0BAIZ,8OAAC,iJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,UAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}