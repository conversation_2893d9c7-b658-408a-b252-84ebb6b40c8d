"use client";

import { useState, useEffect } from "react";
import {
  PlusIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  PencilIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import toast from "react-hot-toast";
import { useAuth } from "@/hooks/useAuth";
import { departmentApi, employeeApi } from "@/lib/api";
import { formatDate } from "@/lib/utils";
import CreateDepartmentModal from "@/components/modals/CreateDepartmentModal";
import EditDepartmentModal from "@/components/modals/EditDepartmentModal";
import type { Department, Employee } from "@/types";

export default function DepartmentsPage() {
  const { user } = useAuth();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] =
    useState<Department | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [departmentsData, employeesData] = await Promise.all([
        departmentApi.getAll(),
        employeeApi.getAll({ limit: 100 }),
      ]);
      setDepartments(departmentsData);
      setEmployees(employeesData);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = (department: Department) => {
    setSelectedDepartment(department);
    setShowEditModal(true);
  };

  const handleDelete = async (department: Department) => {
    const employeeCount = getDepartmentEmployeeCount(department.id);
    if (employeeCount > 0) {
      toast.error("该部门下还有员工，无法删除");
      return;
    }

    if (window.confirm(`确定要删除部门 "${department.department_name}" 吗？`)) {
      try {
        await departmentApi.delete(department.id);
        toast.success("部门删除成功");
        fetchData();
      } catch (error: any) {
        console.error("删除部门失败:", error);
        const message = error.response?.data?.detail || "删除部门失败";
        toast.error(message);
      }
    }
  };

  // 计算每个部门的员工数量
  const getDepartmentEmployeeCount = (departmentId: number) => {
    return employees.filter((emp) => emp.department_id === departmentId).length;
  };

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-300 rounded w-1/4"></div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
        <div className="h-96 bg-gray-300 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">部门管理</h1>
          <p className="mt-2 text-sm text-gray-600">管理组织架构和部门信息</p>
        </div>
        {(user?.is_sales || user?.is_admin) && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5"
          >
            <PlusIcon className="mr-2 h-4 w-4" />
            新建部门
          </button>
        )}
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-blue-50">
                  <BuildingOfficeIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总部门数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {departments.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-green-50">
                  <UserGroupIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总员工数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employees.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-purple-50">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">平均规模</p>
                <p className="text-2xl font-bold text-gray-900">
                  {departments.length > 0
                    ? Math.round(employees.length / departments.length)
                    : 0}
                  人
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 部门列表 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">部门列表</h3>
        </div>

        {departments.length > 0 ? (
          <div className="grid grid-cols-1 gap-6 p-6 sm:grid-cols-2 lg:grid-cols-3">
            {departments.map((department) => {
              const employeeCount = getDepartmentEmployeeCount(department.id);
              const departmentEmployees = employees.filter(
                (emp) => emp.department_id === department.id
              );

              return (
                <div
                  key={department.id}
                  className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <BuildingOfficeIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {department.department_name}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {employeeCount} 名员工
                        </p>
                      </div>
                    </div>
                    {(user?.is_sales || user?.is_admin) && (
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleEdit(department)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                          title="编辑部门"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(department)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
                          title="删除部门"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">创建时间</span>
                      <span className="text-gray-900">
                        {formatDate(department.created_at)}
                      </span>
                    </div>

                    {employeeCount > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">
                          部门成员
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {departmentEmployees.slice(0, 3).map((employee) => (
                            <span
                              key={employee.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {employee.employee_name}
                            </span>
                          ))}
                          {employeeCount > 3 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                              +{employeeCount - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {employeeCount === 0 && (
                      <div className="text-center py-4">
                        <UserGroupIcon className="mx-auto h-8 w-8 text-gray-400" />
                        <p className="mt-1 text-sm text-gray-500">暂无员工</p>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无部门</h3>
            <p className="mt-1 text-sm text-gray-500">
              开始创建您的第一个部门吧！
            </p>
          </div>
        )}
      </div>

      {/* 新建部门模态框 */}
      <CreateDepartmentModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={fetchData}
      />
    </div>
  );
}
