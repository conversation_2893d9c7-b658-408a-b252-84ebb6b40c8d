{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/CreateProjectModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport { XMarkIcon, FolderIcon } from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { projectApi, employeeApi } from \"@/lib/api\";\nimport type { Employee } from \"@/types\";\n\nconst projectSchema = z.object({\n  project_number: z\n    .string()\n    .min(1, \"请输入项目号\")\n    .max(50, \"项目号不能超过50字符\"),\n  project_type: z\n    .string()\n    .min(1, \"请输入项目类型\")\n    .max(100, \"项目类型不能超过100字符\"),\n  customer_abbreviation: z\n    .string()\n    .min(1, \"请输入客户简称\")\n    .max(100, \"客户简称不能超过100字符\"),\n  customer_name: z\n    .string()\n    .min(1, \"请输入客户名称\")\n    .max(200, \"客户名称不能超过200字符\"),\n  project_name: z\n    .string()\n    .min(1, \"请输入项目名称\")\n    .max(200, \"项目名称不能超过200字符\"),\n  employee_id: z.number().min(1, \"请选择销售经理\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface ProjectFormData {\n  project_number: string;\n  project_type: string;\n  customer_abbreviation: string;\n  customer_name: string;\n  project_name: string;\n  employee_id: number;\n  remarks?: string;\n}\n\ninterface CreateProjectModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function CreateProjectModal({\n  isOpen,\n  onClose,\n  onSuccess,\n}: CreateProjectModalProps) {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ProjectFormData>({\n    resolver: zodResolver(projectSchema),\n    defaultValues: {\n      project_number: \"\",\n      project_type: \"\",\n      customer_abbreviation: \"\",\n      customer_name: \"\",\n      project_name: \"\",\n      employee_id: 0,\n      remarks: \"\",\n    },\n  });\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setIsLoading(true);\n        const employeesData = await employeeApi.getAll({ limit: 100 });\n        setEmployees(employeesData.filter((emp) => emp.is_sales));\n      } catch (error) {\n        console.error(\"获取员工列表失败:\", error);\n        toast.error(\"获取员工列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchEmployees();\n    }\n  }, [isOpen]);\n\n  const onSubmit = async (data: ProjectFormData) => {\n    try {\n      setIsSubmitting(true);\n      await projectApi.create(data);\n\n      toast.success(\"项目创建成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"创建项目失败:\", error);\n      const message = error.response?.data?.detail || \"创建项目失败\";\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        新建项目\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">创建新的项目信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 项目号 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目号 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_number\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_number\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"PRJ001\"\n                      />\n                      {errors.project_number && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_number.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 项目类型 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目类型 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_type\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_type\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"Web开发\"\n                      />\n                      {errors.project_type && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_type.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 客户简称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户简称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_abbreviation\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_abbreviation\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC公司\"\n                      />\n                      {errors.customer_abbreviation && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_abbreviation.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 客户名称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户名称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_name\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_name\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC科技有限公司\"\n                      />\n                      {errors.customer_name && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_name.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 项目名称 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目名称 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      {...register(\"project_name\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_name\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      placeholder=\"企业官网建设项目\"\n                    />\n                    {errors.project_name && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.project_name.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 销售经理 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      销售经理 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"employee_id\", { valueAsNumber: true })}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.employee_id\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value={0}>请选择销售经理</option>\n                      {employees.map((employee) => (\n                        <option key={employee.id} value={employee.id}>\n                          {employee.employee_name} -{\" \"}\n                          {employee.department?.department_name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.employee_id && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.employee_id.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={3}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的项目备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          创建中...\n                        </div>\n                      ) : (\n                        \"创建项目\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,gBAAgB,gLAAA,CAAA,IAAC,CACd,MAAM,GACN,GAAG,CAAC,GAAG,UACP,GAAG,CAAC,IAAI;IACX,cAAc,gLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,uBAAuB,gLAAA,CAAA,IAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,eAAe,gLAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,cAAc,gLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAkBe,SAAS,mBAAmB,KAIjB;QAJiB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACe,GAJiB;;IAKzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,gBAAgB;YAChB,cAAc;YACd,uBAAuB;YACvB,eAAe;YACf,cAAc;YACd,aAAa;YACb,SAAS;QACX;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;+DAAiB;oBACrB,IAAI;wBACF,aAAa;wBACb,MAAM,gBAAgB,MAAM,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;4BAAE,OAAO;wBAAI;wBAC5D,aAAa,cAAc,MAAM;2EAAC,CAAC,MAAQ,IAAI,QAAQ;;oBACzD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,QAAQ;gBACV;YACF;QACF;uCAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAChB,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YAExB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;gBAEH,sBAAA;YADhB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;YAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBAC3C,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;;0EACC,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFAC1D,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAErC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,iBAAiB;gEAC9B,WAAW,AAAC,2IAIX,OAHC,OAAO,cAAc,GACjB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,cAAc,kBACpB,6LAAC;gEAAE,WAAU;0EACV,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;kEAMpC,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,eAAe;gEAC5B,WAAW,AAAC,2IAIX,OAHC,OAAO,YAAY,GACf,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,YAAY,kBAClB,6LAAC;gEAAE,WAAU;0EACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAMpC,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,wBAAwB;gEACrC,WAAW,AAAC,2IAIX,OAHC,OAAO,qBAAqB,GACxB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,qBAAqB,kBAC3B,6LAAC;gEAAE,WAAU;0EACV,OAAO,qBAAqB,CAAC,OAAO;;;;;;;;;;;;kEAM3C,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,gBAAgB;gEAC7B,WAAW,AAAC,2IAIX,OAHC,OAAO,aAAa,GAChB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,aAAa,kBACnB,6LAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAOrC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EACzD,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,6LAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,eAAe;wDAC5B,WAAW,AAAC,2IAIX,OAHC,OAAO,YAAY,GACf,mBACA;wDAEN,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,6LAAC;wDAAE,WAAU;kEACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAMlC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EACzD,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,6LAAC;wDACE,GAAG,SAAS,eAAe;4DAAE,eAAe;wDAAK,EAAE;wDACpD,WAAW,AAAC,2IAIX,OAHC,OAAO,WAAW,GACd,mBACA;wDAEN,UAAU;;0EAEV,6LAAC;gEAAO,OAAO;0EAAG;;;;;;4DACjB,UAAU,GAAG,CAAC,CAAC;oEAGX;qFAFH,6LAAC;oEAAyB,OAAO,SAAS,EAAE;;wEACzC,SAAS,aAAa;wEAAC;wEAAG;yEAC1B,uBAAA,SAAS,UAAU,cAAnB,2CAAA,qBAAqB,eAAe;;mEAF1B,SAAS,EAAE;;;;;;;;;;;;oDAM3B,OAAO,WAAW,kBACjB,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0DAMjC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,AAAC,2IAEX,OADC,OAAO,OAAO,GAAG,mBAAmB;wDAEtC,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,6LAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,6LAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB;GAlVwB;;QAclB,iKAAA,CAAA,UAAO;;;KAdW", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/EditProjectModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport { XMarkIcon, FolderIcon } from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { projectApi, employeeApi } from \"@/lib/api\";\nimport type { Project, Employee } from \"@/types\";\n\nconst projectSchema = z.object({\n  project_number: z\n    .string()\n    .min(1, \"请输入项目号\")\n    .max(50, \"项目号不能超过50字符\"),\n  project_type: z\n    .string()\n    .min(1, \"请输入项目类型\")\n    .max(100, \"项目类型不能超过100字符\"),\n  customer_abbreviation: z\n    .string()\n    .min(1, \"请输入客户简称\")\n    .max(100, \"客户简称不能超过100字符\"),\n  customer_name: z\n    .string()\n    .min(1, \"请输入客户名称\")\n    .max(200, \"客户名称不能超过200字符\"),\n  project_name: z\n    .string()\n    .min(1, \"请输入项目名称\")\n    .max(200, \"项目名称不能超过200字符\"),\n  employee_id: z.number().min(1, \"请选择销售经理\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface ProjectFormData {\n  project_number: string;\n  project_type: string;\n  customer_abbreviation: string;\n  customer_name: string;\n  project_name: string;\n  employee_id: number;\n  remarks?: string;\n}\n\ninterface EditProjectModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n  project: Project | null;\n}\n\nexport default function EditProjectModal({\n  isOpen,\n  onClose,\n  onSuccess,\n  project,\n}: EditProjectModalProps) {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n    setValue,\n  } = useForm<ProjectFormData>({\n    resolver: zodResolver(projectSchema),\n  });\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setIsLoading(true);\n        const employeesData = await employeeApi.getAll({ limit: 100 });\n        setEmployees(employeesData.filter((emp) => emp.is_sales));\n      } catch (error) {\n        console.error(\"获取员工列表失败:\", error);\n        toast.error(\"获取员工列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchEmployees();\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    if (project && isOpen) {\n      setValue(\"project_number\", project.project_number);\n      setValue(\"project_type\", project.project_type);\n      setValue(\"customer_abbreviation\", project.customer_abbreviation);\n      setValue(\"customer_name\", project.customer_name);\n      setValue(\"project_name\", project.project_name);\n      setValue(\"employee_id\", project.employee_id);\n      setValue(\"remarks\", project.remarks || \"\");\n    }\n  }, [project, isOpen, setValue]);\n\n  const onSubmit = async (data: ProjectFormData) => {\n    if (!project) return;\n\n    try {\n      await projectApi.update(project.id, data);\n      toast.success(\"项目更新成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"更新项目失败:\", error);\n      const message = error.response?.data?.detail || \"更新项目失败\";\n      toast.error(message);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        编辑项目\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">修改项目信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 项目号 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目号 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_number\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_number\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"PRJ001\"\n                      />\n                      {errors.project_number && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_number.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 项目类型 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        项目类型 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"project_type\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.project_type\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"Web开发\"\n                      />\n                      {errors.project_type && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.project_type.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 客户简称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户简称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_abbreviation\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_abbreviation\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC公司\"\n                      />\n                      {errors.customer_abbreviation && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_abbreviation.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 客户名称 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        客户名称 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"text\"\n                        {...register(\"customer_name\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.customer_name\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                        placeholder=\"ABC科技有限公司\"\n                      />\n                      {errors.customer_name && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.customer_name.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 项目名称 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目名称 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <input\n                      type=\"text\"\n                      {...register(\"project_name\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_name\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      placeholder=\"企业官网建设项目\"\n                    />\n                    {errors.project_name && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.project_name.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 销售经理 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      销售经理 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"employee_id\", { valueAsNumber: true })}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.employee_id\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value={0}>请选择销售经理</option>\n                      {employees.map((employee) => (\n                        <option key={employee.id} value={employee.id}>\n                          {employee.employee_name} -{\" \"}\n                          {employee.department?.department_name}\n                        </option>\n                      ))}\n                    </select>\n                    {errors.employee_id && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.employee_id.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={3}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的项目备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          更新中...\n                        </div>\n                      ) : (\n                        \"更新项目\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYA,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,gBAAgB,gLAAA,CAAA,IAAC,CACd,MAAM,GACN,GAAG,CAAC,GAAG,UACP,GAAG,CAAC,IAAI;IACX,cAAc,gLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,uBAAuB,gLAAA,CAAA,IAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,eAAe,gLAAA,CAAA,IAAC,CACb,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,cAAc,gLAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,KAAK;IACZ,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAmBe,SAAS,iBAAiB,KAKjB;QALiB,EACvC,MAAM,EACN,OAAO,EACP,SAAS,EACT,OAAO,EACe,GALiB;;IAMvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;6DAAiB;oBACrB,IAAI;wBACF,aAAa;wBACb,MAAM,gBAAgB,MAAM,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;4BAAE,OAAO;wBAAI;wBAC5D,aAAa,cAAc,MAAM;yEAAC,CAAC,MAAQ,IAAI,QAAQ;;oBACzD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,QAAQ;gBACV;YACF;QACF;qCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW,QAAQ;gBACrB,SAAS,kBAAkB,QAAQ,cAAc;gBACjD,SAAS,gBAAgB,QAAQ,YAAY;gBAC7C,SAAS,yBAAyB,QAAQ,qBAAqB;gBAC/D,SAAS,iBAAiB,QAAQ,aAAa;gBAC/C,SAAS,gBAAgB,QAAQ,YAAY;gBAC7C,SAAS,eAAe,QAAQ,WAAW;gBAC3C,SAAS,WAAW,QAAQ,OAAO,IAAI;YACzC;QACF;qCAAG;QAAC;QAAS;QAAQ;KAAS;IAE9B,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YACpC,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;gBAEH,sBAAA;YADhB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;YAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,6LAAC,0LAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBAC3C,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;;0EACC,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFAC1D,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAErC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,iBAAiB;gEAC9B,WAAW,AAAC,2IAIX,OAHC,OAAO,cAAc,GACjB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,cAAc,kBACpB,6LAAC;gEAAE,WAAU;0EACV,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;kEAMpC,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,eAAe;gEAC5B,WAAW,AAAC,2IAIX,OAHC,OAAO,YAAY,GACf,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,YAAY,kBAClB,6LAAC;gEAAE,WAAU;0EACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAMpC,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,wBAAwB;gEACrC,WAAW,AAAC,2IAIX,OAHC,OAAO,qBAAqB,GACxB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,qBAAqB,kBAC3B,6LAAC;gEAAE,WAAU;0EACV,OAAO,qBAAqB,CAAC,OAAO;;;;;;;;;;;;kEAM3C,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAA+C;kFACzD,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,6LAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,gBAAgB;gEAC7B,WAAW,AAAC,2IAIX,OAHC,OAAO,aAAa,GAChB,mBACA;gEAEN,aAAY;;;;;;4DAEb,OAAO,aAAa,kBACnB,6LAAC;gEAAE,WAAU;0EACV,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAOrC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EACzD,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,6LAAC;wDACC,MAAK;wDACJ,GAAG,SAAS,eAAe;wDAC5B,WAAW,AAAC,2IAIX,OAHC,OAAO,YAAY,GACf,mBACA;wDAEN,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,6LAAC;wDAAE,WAAU;kEACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAMlC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;;4DAA+C;0EACzD,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,6LAAC;wDACE,GAAG,SAAS,eAAe;4DAAE,eAAe;wDAAK,EAAE;wDACpD,WAAW,AAAC,2IAIX,OAHC,OAAO,WAAW,GACd,mBACA;wDAEN,UAAU;;0EAEV,6LAAC;gEAAO,OAAO;0EAAG;;;;;;4DACjB,UAAU,GAAG,CAAC,CAAC;oEAGX;qFAFH,6LAAC;oEAAyB,OAAO,SAAS,EAAE;;wEACzC,SAAS,aAAa;wEAAC;wEAAG;yEAC1B,uBAAA,SAAS,UAAU,cAAnB,2CAAA,qBAAqB,eAAe;;mEAF1B,SAAS,EAAE;;;;;;;;;;;;oDAM3B,OAAO,WAAW,kBACjB,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0DAMjC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,AAAC,2IAEX,OADC,OAAO,OAAO,GAAG,mBAAmB;wDAEtC,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,6LAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,6LAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,6LAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,6LAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB;GApVwB;;QAelB,iKAAA,CAAA,UAAO;;;KAfW", "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/ViewProjectModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport {\n  XMarkIcon,\n  FolderIcon,\n  UserIcon,\n  CalendarIcon,\n  DocumentTextIcon,\n} from \"@heroicons/react/24/outline\";\nimport { formatDate } from \"@/lib/utils\";\nimport type { Project } from \"@/types\";\n\ninterface ViewProjectModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  project: Project | null;\n}\n\nexport default function ViewProjectModal({\n  isOpen,\n  onClose,\n  project,\n}: ViewProjectModalProps) {\n  if (!project) return null;\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={onClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        项目详情\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">查看项目详细信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={onClose}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <div className=\"space-y-6\">\n                  {/* 基本信息 */}\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <DocumentTextIcon className=\"h-4 w-4 mr-2\" />\n                      基本信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          项目号\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.project_number}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          项目类型\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.project_type}\n                        </p>\n                      </div>\n                      <div className=\"sm:col-span-2\">\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          项目名称\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.project_name}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 客户信息 */}\n                  <div className=\"bg-blue-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <UserIcon className=\"h-4 w-4 mr-2\" />\n                      客户信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          客户简称\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.customer_abbreviation}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          客户全称\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.customer_name}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 管理信息 */}\n                  <div className=\"bg-green-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <UserIcon className=\"h-4 w-4 mr-2\" />\n                      管理信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          销售经理\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.employee?.employee_name || \"未指定\"}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          所属部门\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {project.employee?.department?.department_name ||\n                            \"未知\"}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 时间信息 */}\n                  <div className=\"bg-purple-50 rounded-lg p-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                      <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                      时间信息\n                    </h4>\n                    <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          创建时间\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {formatDate(project.created_at)}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-500 uppercase tracking-wide\">\n                          更新时间\n                        </label>\n                        <p className=\"mt-1 text-sm text-gray-900\">\n                          {formatDate(project.updated_at)}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* 备注信息 */}\n                  {project.remarks && (\n                    <div className=\"bg-yellow-50 rounded-lg p-4\">\n                      <h4 className=\"text-sm font-medium text-gray-900 mb-3 flex items-center\">\n                        <DocumentTextIcon className=\"h-4 w-4 mr-2\" />\n                        备注信息\n                      </h4>\n                      <p className=\"text-sm text-gray-700\">{project.remarks}</p>\n                    </div>\n                  )}\n\n                  {/* 关闭按钮 */}\n                  <div className=\"flex items-center justify-end pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={onClose}\n                    >\n                      关闭\n                    </button>\n                  </div>\n                </div>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAXA;;;;;;AAoBe,SAAS,iBAAiB,KAIjB;QAJiB,EACvC,MAAM,EACN,OAAO,EACP,OAAO,EACe,GAJiB;QAmId,mBAQA,8BAAA;IAtIzB,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC,0LAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,6JAAA,CAAA,WAAQ;kBAC3C,cAAA,6LAAC,kLAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,6JAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,6JAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;;0EACC,6LAAC,kLAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;0DAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,kOAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,QAAQ,cAAc;;;;;;;;;;;;0EAG3B,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,QAAQ,YAAY;;;;;;;;;;;;0EAGzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAO7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,QAAQ,qBAAqB;;;;;;;;;;;;0EAGlC,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;0DAO9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,EAAA,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,aAAa,KAAI;;;;;;;;;;;;0EAGxC,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,EAAA,qBAAA,QAAQ,QAAQ,cAAhB,0CAAA,+BAAA,mBAAkB,UAAU,cAA5B,mDAAA,6BAA8B,eAAe,KAC5C;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;0EAGlC,6LAAC;;kFACC,6LAAC;wEAAM,WAAU;kFAAkE;;;;;;kFAGnF,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;;;4CAOrC,QAAQ,OAAO,kBACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,kOAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAG/C,6LAAC;wDAAE,WAAU;kEAAyB,QAAQ,OAAO;;;;;;;;;;;;0DAKzD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;KAzMwB", "debugId": null}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/projects/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  FolderIcon,\n  UserIcon,\n  CalendarIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n} from \"@heroicons/react/24/outline\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { projectApi, employeeApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\nimport CreateProjectModal from \"@/components/modals/CreateProjectModal\";\nimport EditProjectModal from \"@/components/modals/EditProjectModal\";\nimport ViewProjectModal from \"@/components/modals/ViewProjectModal\";\nimport ConfirmDialog from \"@/components/ConfirmDialog\";\nimport type { Project, Employee } from \"@/types\";\n\nexport default function ProjectsPage() {\n  const { user } = useAuth();\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedType, setSelectedType] = useState(\"\");\n  const [selectedEmployee, setSelectedEmployee] = useState(\"\");\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [projectsData, employeesData] = await Promise.all([\n        projectApi.getAll({ limit: 100 }),\n        employeeApi.getAll({ limit: 100 }),\n      ]);\n      setProjects(projectsData);\n      setEmployees(employeesData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const handleView = (project: Project) => {\n    setSelectedProject(project);\n    setShowViewModal(true);\n  };\n\n  const handleEdit = (project: Project) => {\n    setSelectedProject(project);\n    setShowEditModal(true);\n  };\n\n  const handleDelete = (project: Project) => {\n    setProjectToDelete(project);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDelete = async () => {\n    if (!projectToDelete) return;\n\n    try {\n      setIsDeleting(true);\n      await projectApi.delete(projectToDelete.id);\n      toast.success(\"项目删除成功\");\n      fetchData();\n      setShowDeleteConfirm(false);\n      setProjectToDelete(null);\n    } catch (error: any) {\n      console.error(\"删除项目失败:\", error);\n      const message = error.response?.data?.detail || \"删除项目失败\";\n      toast.error(message);\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const filteredProjects = projects.filter((project) => {\n    const matchesSearch =\n      project.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      project.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      project.project_number.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesType = !selectedType || project.project_type === selectedType;\n    const matchesEmployee =\n      !selectedEmployee || project.employee_id.toString() === selectedEmployee;\n\n    return matchesSearch && matchesType && matchesEmployee;\n  });\n\n  const projectTypes = [...new Set(projects.map((p) => p.project_type))];\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">项目管理</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">管理和跟踪所有项目信息</p>\n        </div>\n        <button\n          onClick={() => setShowCreateModal(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\"\n        >\n          <PlusIcon className=\"mr-2 h-4 w-4\" />\n          新建项目\n        </button>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-3\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <FolderIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总项目数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredProjects.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <UserIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">销售经理</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {new Set(projects.map((p) => p.employee_id)).size}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <CalendarIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">项目类型</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {projectTypes.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 筛选器 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              搜索\n            </label>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                placeholder=\"搜索项目名称、客户或项目号...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              项目类型\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedType}\n              onChange={(e) => setSelectedType(e.target.value)}\n            >\n              <option value=\"\">全部类型</option>\n              {projectTypes.map((type) => (\n                <option key={type} value={type}>\n                  {type}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              销售经理\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedEmployee}\n              onChange={(e) => setSelectedEmployee(e.target.value)}\n            >\n              <option value=\"\">全部经理</option>\n              {employees\n                .filter((emp) => emp.is_sales)\n                .map((employee) => (\n                  <option key={employee.id} value={employee.id.toString()}>\n                    {employee.employee_name}\n                  </option>\n                ))}\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm(\"\");\n                setSelectedType(\"\");\n                setSelectedEmployee(\"\");\n              }}\n              className=\"w-full px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n            >\n              重置筛选\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 项目列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">项目列表</h3>\n        </div>\n\n        {filteredProjects.length > 0 ? (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    项目信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    客户信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    销售经理\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    创建时间\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredProjects.map((project) => (\n                  <tr\n                    key={project.id}\n                    className=\"hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"flex items-center\">\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2\">\n                            {project.project_number}\n                          </span>\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                            {project.project_type}\n                          </span>\n                        </div>\n                        <div className=\"text-sm font-medium text-gray-900 mt-1\">\n                          {project.project_name}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {project.customer_name}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {project.customer_abbreviation}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {project.employee?.employee_name}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {project.employee?.department?.department_name}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(project.created_at)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button\n                          onClick={() => handleView(project)}\n                          className=\"text-blue-600 hover:text-blue-800 transition-colors duration-200\"\n                          title=\"查看详情\"\n                        >\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleEdit(project)}\n                          className=\"text-green-600 hover:text-green-800 transition-colors duration-200\"\n                          title=\"编辑项目\"\n                        >\n                          <PencilIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleDelete(project)}\n                          className=\"text-red-600 hover:text-red-800 transition-colors duration-200\"\n                          title=\"删除项目\"\n                        >\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <FolderIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无项目</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedType || selectedEmployee\n                ? \"没有找到符合条件的项目\"\n                : \"开始创建您的第一个项目吧！\"}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* 新建项目模态框 */}\n      <CreateProjectModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={fetchData}\n      />\n\n      {/* 编辑项目模态框 */}\n      <EditProjectModal\n        isOpen={showEditModal}\n        onClose={() => setShowEditModal(false)}\n        onSuccess={fetchData}\n        project={selectedProject}\n      />\n\n      {/* 查看项目模态框 */}\n      <ViewProjectModal\n        isOpen={showViewModal}\n        onClose={() => setShowViewModal(false)}\n        project={selectedProject}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;AAuBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,cAAc,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtD,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAC/B,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;aACjC;YACD,YAAY;YACZ,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,cAAc;YACd,MAAM,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAC1C,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,OAAY;gBAEH,sBAAA;YADhB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;YAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,gBACJ,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,QAAQ,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,QAAQ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtE,MAAM,cAAc,CAAC,gBAAgB,QAAQ,YAAY,KAAK;QAC9D,MAAM,kBACJ,CAAC,oBAAoB,QAAQ,WAAW,CAAC,QAAQ,OAAO;QAE1D,OAAO,iBAAiB,eAAe;IACzC;IAEA,MAAM,eAAe;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY;KAAG;IAEtE,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAE5C,6LAAC;wBACC,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;0CAEV,6LAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,WAAW,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKnD,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sDAE/C,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;gDAAkB,OAAO;0DACvB;+CADU;;;;;;;;;;;;;;;;;sCAOnB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;;sDAEnD,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,UACE,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,EAC5B,GAAG,CAAC,CAAC,yBACJ,6LAAC;gDAAyB,OAAO,SAAS,EAAE,CAAC,QAAQ;0DAClD,SAAS,aAAa;+CADZ,SAAS,EAAE;;;;;;;;;;;;;;;;;sCAOhC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,cAAc;oCACd,gBAAgB;oCAChB,oBAAoB;gCACtB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,iBAAiB,MAAM,GAAG,kBACzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,iBAAiB,GAAG,CAAC,CAAC;4CA8Bd,mBAGA,8BAAA;6DAhCP,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFACb,QAAQ,cAAc;;;;;;kFAEzB,6LAAC;wEAAK,WAAU;kFACb,QAAQ,YAAY;;;;;;;;;;;;0EAGzB,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,YAAY;;;;;;;;;;;;;;;;;8DAI3B,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,aAAa;;;;;;sEAExB,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,qBAAqB;;;;;;;;;;;;8DAGlC,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;uEACZ,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,aAAa;;;;;;sEAElC,6LAAC;4DAAI,WAAU;uEACZ,qBAAA,QAAQ,QAAQ,cAAhB,0CAAA,+BAAA,mBAAkB,UAAU,cAA5B,mDAAA,6BAA8B,eAAe;;;;;;;;;;;;8DAGlD,6LAAC;oDAAG,WAAU;8DACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;8DAEhC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,WAAW;gEAC1B,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;0EAErB,6LAAC;gEACC,SAAS,IAAM,WAAW;gEAC1B,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,6LAAC;gEACC,SAAS,IAAM,aAAa;gEAC5B,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA1DtB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;6CAoEzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,cAAc,gBAAgB,mBAC3B,gBACA;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC,qJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,WAAW;;;;;;0BAIb,6LAAC,mJAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,mJAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,SAAS;;;;;;;;;;;;AAIjB;GA5XwB;;QACL,0HAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}