{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from \"axios\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport type {\n  LoginRequest,\n  Token,\n  User,\n  Department,\n  DepartmentCreate,\n  DepartmentUpdate,\n  Employee,\n  EmployeeCreate,\n  EmployeeUpdate,\n  Project,\n  ProjectCreate,\n  ProjectUpdate,\n  WorkLog,\n  WorkLogCreate,\n  WorkLogUpdate,\n  WorkLogStatistics,\n  WorkLogQueryParams,\n} from \"@/types\";\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: `${API_BASE_URL}/api/v1`,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器 - 添加认证 token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = Cookies.get(\"access_token\");\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // 未授权，清除 token 并跳转到登录页\n      Cookies.remove(\"access_token\");\n      window.location.href = \"/login\";\n    } else if (error.response?.data?.detail) {\n      // 显示服务器错误信息\n      toast.error(error.response.data.detail);\n    } else {\n      // 显示通用错误信息\n      toast.error(\"请求失败，请稍后重试\");\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 认证 API\nexport const authApi = {\n  login: async (data: LoginRequest): Promise<Token> => {\n    const response = await apiClient.post<Token>(\"/auth/login\", data);\n    return response.data;\n  },\n\n  getCurrentUser: async (): Promise<User> => {\n    const response = await apiClient.get<User>(\"/employees/me\");\n    return response.data;\n  },\n\n  changePassword: async (data: {\n    current_password: string;\n    new_password: string;\n  }): Promise<{ message: string }> => {\n    const response = await apiClient.post<{ message: string }>(\n      \"/auth/change-password\",\n      data\n    );\n    return response.data;\n  },\n};\n\n// 部门 API\nexport const departmentApi = {\n  getAll: async (): Promise<Department[]> => {\n    const response = await apiClient.get<Department[]>(\"/departments/\");\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Department> => {\n    const response = await apiClient.get<Department>(`/departments/${id}`);\n    return response.data;\n  },\n\n  create: async (data: DepartmentCreate): Promise<Department> => {\n    const response = await apiClient.post<Department>(\"/departments/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: DepartmentUpdate): Promise<Department> => {\n    const response = await apiClient.put<Department>(\n      `/departments/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/departments/${id}`);\n  },\n};\n\n// 员工 API\nexport const employeeApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    department_id?: number;\n  }): Promise<Employee[]> => {\n    const response = await apiClient.get<Employee[]>(\"/employees/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Employee> => {\n    const response = await apiClient.get<Employee>(`/employees/${id}`);\n    return response.data;\n  },\n\n  create: async (data: EmployeeCreate): Promise<Employee> => {\n    const response = await apiClient.post<Employee>(\"/employees/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: EmployeeUpdate): Promise<Employee> => {\n    const response = await apiClient.put<Employee>(`/employees/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/employees/${id}`);\n  },\n};\n\n// 项目 API\nexport const projectApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    manager_id?: number;\n    project_type?: string;\n  }): Promise<Project[]> => {\n    const response = await apiClient.get<Project[]>(\"/projects/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Project> => {\n    const response = await apiClient.get<Project>(`/projects/${id}`);\n    return response.data;\n  },\n\n  getByNumber: async (projectNumber: string): Promise<Project> => {\n    const response = await apiClient.get<Project>(\n      `/projects/by-number/${projectNumber}`\n    );\n    return response.data;\n  },\n\n  create: async (data: ProjectCreate): Promise<Project> => {\n    const response = await apiClient.post<Project>(\"/projects/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: ProjectUpdate): Promise<Project> => {\n    const response = await apiClient.put<Project>(`/projects/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/projects/${id}`);\n  },\n};\n\n// 工作日志 API\nexport const workLogApi = {\n  getAll: async (params?: WorkLogQueryParams): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/\", { params });\n    return response.data;\n  },\n\n  getMy: async (\n    params?: Omit<WorkLogQueryParams, \"employee_id\">\n  ): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/my\", {\n      params,\n    });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<WorkLog> => {\n    const response = await apiClient.get<WorkLog>(`/work-logs/${id}`);\n    return response.data;\n  },\n\n  getStatistics: async (\n    params?: Omit<WorkLogQueryParams, \"skip\" | \"limit\">\n  ): Promise<WorkLogStatistics> => {\n    const response = await apiClient.get<WorkLogStatistics>(\n      \"/work-logs/statistics\",\n      { params }\n    );\n    return response.data;\n  },\n\n  create: async (data: WorkLogCreate): Promise<WorkLog> => {\n    const response = await apiClient.post<WorkLog>(\"/work-logs/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: WorkLogUpdate): Promise<WorkLog> => {\n    const response = await apiClient.put<WorkLog>(`/work-logs/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/work-logs/${id}`);\n  },\n};\n\nexport default apiClient;\n"], "names": [], "mappings": ";;;;;;;;AAwBqB;AAxBrB;AACA;AACA;;;;AAqBA,WAAW;AACX,MAAM,eAAe,6DAAmC;AAExD,cAAc;AACd,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS,AAAC,GAAe,OAAb,cAAa;IACzB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qBAAqB;AACrB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;IAC3C;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;QACK,iBAIO,sBAAA;IAJX,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;QAClC,uBAAuB;QACvB,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,OAAO,KAAI,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,EAAE;QACvC,YAAY;QACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;IACxC,OAAO;QACL,WAAW;QACX,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;IACd;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,UAAU,IAAI,CAAQ,eAAe;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAO;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QAIrB,MAAM,WAAW,MAAM,UAAU,IAAI,CACnC,yBACA;QAEF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ;QACN,MAAM,WAAW,MAAM,UAAU,GAAG,CAAe;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,AAAC,gBAAkB,OAAH;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAa,iBAAiB;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,AAAC,gBAAkB,OAAH,KAChB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,AAAC,gBAAkB,OAAH;IACzC;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,OAAO;QAKb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,eAAe;YAAE;QAAO;QACzE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,AAAC,cAAgB,OAAH;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAW,eAAe;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,AAAC,cAAgB,OAAH,KAAM;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,AAAC,cAAgB,OAAH;IACvC;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QAMb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,cAAc;YAAE;QAAO;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,AAAC,aAAe,OAAH;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,AAAC,uBAAoC,OAAd;QAEzB,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,AAAC,aAAe,OAAH,KAAM;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,AAAC,aAAe,OAAH;IACtC;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,eAAe;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OACL;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,iBAAiB;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,AAAC,cAAgB,OAAH;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe,OACb;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,yBACA;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,eAAe;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,AAAC,cAAgB,OAAH,KAAM;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,AAAC,cAAgB,OAAH;IACvC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport { authApi } from \"@/lib/api\";\nimport type { User, LoginRequest } from \"@/types\";\n\ninterface UseAuthReturn {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n  hasPermission: (permission: string) => boolean;\n  canManageEmployees: () => boolean;\n  canManageProjects: () => boolean;\n  canManageDepartments: () => boolean;\n  canAccessSettings: () => boolean;\n  canViewAllLogs: () => boolean;\n}\n\nexport function useAuth(): UseAuthReturn {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  const isAuthenticated = !!user;\n\n  // 获取当前用户信息\n  const fetchUser = useCallback(async () => {\n    try {\n      const token = Cookies.get(\"access_token\");\n      if (!token) {\n        setIsLoading(false);\n        return;\n      }\n\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"获取用户信息失败:\", error);\n      Cookies.remove(\"access_token\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // 登录\n  const login = useCallback(\n    async (credentials: LoginRequest) => {\n      try {\n        setIsLoading(true);\n        const tokenData = await authApi.login(credentials);\n\n        // 保存 token\n        Cookies.set(\"access_token\", tokenData.access_token, {\n          expires: 1, // 1天过期\n          secure: process.env.NODE_ENV === \"production\",\n          sameSite: \"strict\",\n        });\n\n        // 获取用户信息\n        const userData = await authApi.getCurrentUser();\n        setUser(userData);\n\n        toast.success(\"登录成功\");\n        router.push(\"/dashboard\");\n      } catch (error: any) {\n        console.error(\"登录失败:\", error);\n        const message =\n          error.response?.data?.detail || \"登录失败，请检查用户名和密码\";\n        toast.error(message);\n        throw error;\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [router]\n  );\n\n  // 登出\n  const logout = useCallback(() => {\n    Cookies.remove(\"access_token\");\n    setUser(null);\n    toast.success(\"已退出登录\");\n    router.push(\"/login\");\n  }, [router]);\n\n  // 刷新用户信息\n  const refreshUser = useCallback(async () => {\n    try {\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"刷新用户信息失败:\", error);\n    }\n  }, []);\n\n  // 权限检查函数\n  const hasPermission = useCallback(\n    (permission: string) => {\n      if (!user) return false;\n\n      // 管理员（admin）具有所有权限\n      if (user.is_admin) return true;\n\n      switch (permission) {\n        case \"manage_employees\":\n          return user.is_admin; // 只有管理员可以管理员工\n        case \"manage_projects\":\n          return true; // 所有员工都可以管理项目\n        case \"manage_departments\":\n          return user.is_sales || user.is_admin; // 销售人员和管理员可以管理部门\n        case \"access_settings\":\n          return user.is_admin; // 只有管理员可以访问系统设置\n        case \"view_all_logs\":\n          return user.is_admin; // 只有管理员可以查看所有日志\n        default:\n          return false;\n      }\n    },\n    [user]\n  );\n\n  const canManageEmployees = useCallback(\n    () => hasPermission(\"manage_employees\"),\n    [hasPermission]\n  );\n  const canManageProjects = useCallback(\n    () => hasPermission(\"manage_projects\"),\n    [hasPermission]\n  );\n  const canManageDepartments = useCallback(\n    () => hasPermission(\"manage_departments\"),\n    [hasPermission]\n  );\n  const canAccessSettings = useCallback(\n    () => hasPermission(\"access_settings\"),\n    [hasPermission]\n  );\n  const canViewAllLogs = useCallback(\n    () => hasPermission(\"view_all_logs\"),\n    [hasPermission]\n  );\n\n  // 初始化时检查登录状态\n  useEffect(() => {\n    fetchUser();\n  }, [fetchUser]);\n\n  return {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    logout,\n    refreshUser,\n    hasPermission,\n    canManageEmployees,\n    canManageProjects,\n    canManageDepartments,\n    canAccessSettings,\n    canViewAllLogs,\n  };\n}\n"], "names": [], "mappings": ";;;AA4DkB;AA1DlB;AACA;AACA;AACA;AACA;;AANA;;;;;;AAwBO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC,CAAC;IAE1B,WAAW;IACX,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YAC5B,IAAI;gBACF,MAAM,QAAQ,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;gBAC1B,IAAI,CAAC,OAAO;oBACV,aAAa;oBACb;gBACF;gBAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;gBAC7C,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACjB,SAAU;gBACR,aAAa;YACf;QACF;yCAAG,EAAE;IAEL,KAAK;IACL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sCACtB,OAAO;YACL,IAAI;gBACF,aAAa;gBACb,MAAM,YAAY,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAEtC,WAAW;gBACX,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,YAAY,EAAE;oBAClD,SAAS;oBACT,QAAQ,oDAAyB;oBACjC,UAAU;gBACZ;gBAEA,SAAS;gBACT,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;gBAC7C,QAAQ;gBAER,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAY;oBAGjB,sBAAA;gBAFF,QAAQ,KAAK,CAAC,SAAS;gBACvB,MAAM,UACJ,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAClC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,aAAa;YACf;QACF;qCACA;QAAC;KAAO;IAGV,KAAK;IACL,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE;YACzB,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACf,QAAQ;YACR,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd;sCAAG;QAAC;KAAO;IAEX,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YAC9B,IAAI;gBACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;gBAC7C,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;2CAAG,EAAE;IAEL,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAC9B,CAAC;YACC,IAAI,CAAC,MAAM,OAAO;YAElB,mBAAmB;YACnB,IAAI,KAAK,QAAQ,EAAE,OAAO;YAE1B,OAAQ;gBACN,KAAK;oBACH,OAAO,KAAK,QAAQ,EAAE,cAAc;gBACtC,KAAK;oBACH,OAAO,MAAM,cAAc;gBAC7B,KAAK;oBACH,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,iBAAiB;gBAC1D,KAAK;oBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;gBACxC,KAAK;oBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;gBACxC;oBACE,OAAO;YACX;QACF;6CACA;QAAC;KAAK;IAGR,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDACnC,IAAM,cAAc;kDACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAClC,IAAM,cAAc;iDACpB;QAAC;KAAc;IAEjB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDACrC,IAAM,cAAc;oDACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAClC,IAAM,cAAc;iDACpB;QAAC;KAAc;IAEjB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAC/B,IAAM,cAAc;8CACpB;QAAC;KAAc;IAGjB,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR;QACF;4BAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA/IgB;;QAGC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\n\nexport default function Home() {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,iBAAiB;oBACnB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;GAnBwB;;QACiB,0HAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}