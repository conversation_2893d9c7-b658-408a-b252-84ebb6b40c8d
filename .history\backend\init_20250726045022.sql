-- 员工工时日志管理系统数据库初始化脚本
-- 生成时间: 2025-07-26

-- 创建部门表
CREATE TABLE IF NOT EXISTS department (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department_name VARCHAR(100) NOT NULL,
    delete_flag BOOLEAN NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建部门索引
CREATE INDEX IF NOT EXISTS ix_department_id ON department (id);
CREATE INDEX IF NOT EXISTS ix_department_department_name ON department (department_name);
CREATE INDEX IF NOT EXISTS ix_department_delete_flag ON department (delete_flag);

-- 创建员工表
CREATE TABLE IF NOT EXISTS employee (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_name VARCHAR(128) NOT NULL,
    password VARCHAR(128) NOT NULL,
    is_sales BOOLEAN NOT NULL DEFAULT 0,
    is_admin BOOLEAN NOT NULL DEFAULT 0,
    department_id INTEGER NOT NULL,
    remarks VARCHAR(500),
    delete_flag BOOLEAN NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(department_id) REFERENCES department (id) ON DELETE RESTRICT
);

-- 创建员工索引
CREATE INDEX IF NOT EXISTS ix_employee_id ON employee (id);
CREATE INDEX IF NOT EXISTS ix_employee_employee_name ON employee (employee_name);
CREATE INDEX IF NOT EXISTS ix_employee_department_id ON employee (department_id);
CREATE INDEX IF NOT EXISTS ix_employee_delete_flag ON employee (delete_flag);

-- 创建项目表
CREATE TABLE IF NOT EXISTS project (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_number VARCHAR(50) NOT NULL UNIQUE,
    project_type VARCHAR(100) NOT NULL,
    customer_abbreviation VARCHAR(100) NOT NULL,
    customer_name VARCHAR(200) NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    employee_id INTEGER NOT NULL,
    remarks VARCHAR(500),
    delete_flag BOOLEAN NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(employee_id) REFERENCES employee (id) ON DELETE RESTRICT
);

-- 创建项目索引
CREATE UNIQUE INDEX IF NOT EXISTS ix_project_project_number ON project (project_number);
CREATE INDEX IF NOT EXISTS ix_project_id ON project (id);
CREATE INDEX IF NOT EXISTS ix_project_employee_id ON project (employee_id);
CREATE INDEX IF NOT EXISTS ix_project_delete_flag ON project (delete_flag);

-- 创建工作日志表
CREATE TABLE IF NOT EXISTS work_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    work_date DATE NOT NULL,
    employee_id INTEGER NOT NULL,
    project_number VARCHAR(50) NOT NULL,
    work_hours DECIMAL(5, 2) NOT NULL,
    work_content TEXT NOT NULL,
    remarks VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_work_hours CHECK (work_hours >= 0 AND work_hours <= 24),
    CONSTRAINT idx_unique_worklog UNIQUE (work_date, employee_id, project_number),
    FOREIGN KEY(employee_id) REFERENCES employee (id) ON DELETE RESTRICT,
    FOREIGN KEY(project_number) REFERENCES project (project_number) ON DELETE RESTRICT
);

-- 创建工作日志索引
CREATE INDEX IF NOT EXISTS ix_work_log_id ON work_log (id);
CREATE INDEX IF NOT EXISTS ix_work_log_work_date ON work_log (work_date);
CREATE INDEX IF NOT EXISTS ix_work_log_employee_id ON work_log (employee_id);
CREATE INDEX IF NOT EXISTS ix_work_log_project_number ON work_log (project_number);

-- 插入初始部门数据
INSERT OR IGNORE INTO department (id, department_name) VALUES 
(1, '技术部'),
(2, '销售部'),
(3, '市场部'),
(4, '人事部'),
(5, '财务部');

-- 插入初始员工数据（密码已加密）
INSERT OR IGNORE INTO employee (id, employee_name, password, is_sales, is_admin, department_id, remarks) VALUES 
(1, 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 1, 1, 2, '系统管理员'),
(2, '张三', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 0, 0, 1, '技术部主管'),
(3, '李四', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 1, 0, 2, '销售经理'),
(4, '王五', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 0, 0, 1, '前端开发工程师'),
(5, '赵六', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 1, 0, 2, '销售代表');

-- 插入初始项目数据
INSERT OR IGNORE INTO project (id, project_number, project_type, customer_abbreviation, customer_name, project_name, manager_id, remarks) VALUES 
(1, 'PRJ001', 'Web开发', 'ABC公司', 'ABC科技有限公司', '企业官网建设项目', 3, '企业官网重构项目'),
(2, 'PRJ002', '移动应用', 'XYZ集团', 'XYZ集团有限公司', '移动端APP开发', 3, '电商类移动应用'),
(3, 'PRJ003', '系统集成', 'DEF企业', 'DEF企业管理有限公司', 'ERP系统实施', 3, 'ERP系统定制开发');

-- 插入示例工作日志数据
INSERT OR IGNORE INTO work_log (work_date, employee_id, project_number, work_hours, work_content, remarks) VALUES 
('2025-07-25', 2, 'PRJ001', 8.0, '完成首页设计和开发', '进度正常'),
('2025-07-25', 4, 'PRJ001', 7.5, '实现用户登录功能', '需要优化性能'),
('2025-07-24', 2, 'PRJ002', 6.0, '移动端界面适配', ''),
('2025-07-24', 4, 'PRJ002', 8.0, 'API接口开发', '已完成用户模块'),
('2025-07-23', 2, 'PRJ003', 4.0, 'ERP系统需求分析', '与客户沟通确认'),
('2025-07-23', 3, 'PRJ001', 2.0, '项目进度跟踪', '客户满意度较高'),
('2025-07-22', 2, 'PRJ001', 8.0, '数据库设计优化', '性能提升明显'),
('2025-07-22', 4, 'PRJ003', 6.5, '系统架构设计', '技术方案已确定'),
('2025-07-21', 2, 'PRJ002', 7.0, '移动端测试', '发现并修复3个bug'),
('2025-07-21', 4, 'PRJ001', 8.0, '前端组件开发', '完成80%功能');

-- 更新序列值（SQLite 自动处理）
-- 注意：SQLite 使用 AUTOINCREMENT 自动管理序列

-- 创建视图：员工工时统计
CREATE VIEW IF NOT EXISTS employee_work_stats AS
SELECT 
    e.id,
    e.employee_name,
    d.department_name,
    COUNT(wl.id) as total_logs,
    COALESCE(SUM(wl.work_hours), 0) as total_hours,
    COALESCE(AVG(wl.work_hours), 0) as avg_hours_per_day
FROM employee e
LEFT JOIN department d ON e.department_id = d.id
LEFT JOIN work_log wl ON e.id = wl.employee_id
GROUP BY e.id, e.employee_name, d.department_name;

-- 创建视图：项目工时统计
CREATE VIEW IF NOT EXISTS project_work_stats AS
SELECT 
    p.project_number,
    p.project_name,
    p.customer_name,
    e.employee_name as manager_name,
    COUNT(wl.id) as total_logs,
    COALESCE(SUM(wl.work_hours), 0) as total_hours,
    COUNT(DISTINCT wl.employee_id) as employee_count
FROM project p
LEFT JOIN employee e ON p.manager_id = e.id
LEFT JOIN work_log wl ON p.project_number = wl.project_number
GROUP BY p.project_number, p.project_name, p.customer_name, e.employee_name;

-- 创建视图：月度工时统计
CREATE VIEW IF NOT EXISTS monthly_work_stats AS
SELECT 
    strftime('%Y-%m', wl.work_date) as month,
    COUNT(wl.id) as total_logs,
    SUM(wl.work_hours) as total_hours,
    COUNT(DISTINCT wl.employee_id) as active_employees,
    COUNT(DISTINCT wl.project_number) as active_projects
FROM work_log wl
GROUP BY strftime('%Y-%m', wl.work_date)
ORDER BY month DESC;

-- 设置数据库版本信息
PRAGMA user_version = 1;

-- 数据库初始化完成
-- 默认密码为: 123456 (admin账号密码为: admin123)
-- 所有密码的哈希值: $2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS
