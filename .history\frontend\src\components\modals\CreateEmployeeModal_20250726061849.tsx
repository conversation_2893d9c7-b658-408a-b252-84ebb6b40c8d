"use client";

import { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, UserIcon } from "@heroicons/react/24/outline";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import { employeeApi, departmentApi } from "@/lib/api";
import type { Department } from "@/types";

const employeeSchema = z.object({
  employee_name: z
    .string()
    .min(1, "请输入员工姓名")
    .max(100, "员工姓名不能超过100字符"),
  password: z.string().min(6, "密码至少6位").max(100, "密码不能超过100字符"),
  is_sales: z.boolean(),
  is_admin: z.boolean(),
  department_id: z.number().min(1, "请选择部门"),
  remarks: z.string().max(500, "备注不能超过500字符").optional(),
});

interface EmployeeFormData {
  employee_name: string;
  password: string;
  is_sales: boolean;
  is_admin: boolean;
  department_id: number;
  remarks?: string;
}

interface CreateEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateEmployeeModal({
  isOpen,
  onClose,
  onSuccess,
}: CreateEmployeeModalProps) {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      employee_name: "",
      password: "",
      is_sales: false,
      is_admin: false,
      department_id: 0,
      remarks: "",
    },
  });

  const watchedIsSales = watch("is_sales");
  const watchedIsAdmin = watch("is_admin");

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setIsLoading(true);
        const departmentsData = await departmentApi.getAll();
        setDepartments(departmentsData);
      } catch (error) {
        console.error("获取部门列表失败:", error);
        toast.error("获取部门列表失败");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchDepartments();
    }
  }, [isOpen]);

  const onSubmit = async (data: EmployeeFormData) => {
    try {
      await employeeApi.create(data);
      toast.success("员工创建成功");
      reset();
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("创建员工失败:", error);
      const message = error.response?.data?.detail || "创建员工失败";
      toast.error(message);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <UserIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold text-gray-900"
                      >
                        新建员工
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">创建新的员工账号</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* 员工姓名 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        员工姓名 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        {...register("employee_name")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.employee_name
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        placeholder="请输入员工姓名"
                      />
                      {errors.employee_name && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.employee_name.message}
                        </p>
                      )}
                    </div>

                    {/* 登录密码 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        登录密码 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="password"
                        {...register("password")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.password ? "border-red-300" : "border-gray-300"
                        }`}
                        placeholder="请输入登录密码"
                      />
                      {errors.password && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.password.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* 所属部门 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        所属部门 <span className="text-red-500">*</span>
                      </label>
                      <select
                        {...register("department_id", { valueAsNumber: true })}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.department_id
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                        disabled={isLoading}
                      >
                        <option value={0}>请选择部门</option>
                        {departments.map((department) => (
                          <option key={department.id} value={department.id}>
                            {department.department_name}
                          </option>
                        ))}
                      </select>
                      {errors.department_id && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.department_id.message}
                        </p>
                      )}
                    </div>

                    {/* 员工角色 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        员工角色
                      </label>
                      <div className="flex items-center space-x-4 pt-2">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            {...register("is_sales", { valueAsNumber: false })}
                            value="false"
                            defaultChecked={true}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            普通员工
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            {...register("is_sales", { valueAsNumber: false })}
                            value="true"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            销售人员
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* 管理员权限 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      管理员权限
                    </label>
                    <div className="flex items-center space-x-4 pt-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          {...register("is_admin", { valueAsNumber: false })}
                          value="false"
                          defaultChecked={true}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          普通权限
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          {...register("is_admin", { valueAsNumber: false })}
                          value="true"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          管理员权限
                        </span>
                      </label>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      管理员可以查看所有员工的工作日志和管理系统
                    </p>
                  </div>

                  {/* 备注 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      备注
                    </label>
                    <textarea
                      {...register("remarks")}
                      rows={3}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.remarks ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="可选的员工备注信息..."
                    />
                    {errors.remarks && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.remarks.message}
                      </p>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || isLoading}
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          创建中...
                        </div>
                      ) : (
                        "创建员工"
                      )}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
