"""工作日志模型"""
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, Text, DECIMAL, CheckConstraint, UniqueConstraint, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class WorkLog(Base):
    """员工工作日志表"""
    __tablename__ = "work_log"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    work_date = Column(Date, nullable=False, index=True, comment="工作日期")
    employee_id = Column(
        Integer, 
        ForeignKey("employee.id", ondelete="RESTRICT"), 
        nullable=False,
        index=True,
        comment="员工ID"
    )
    project_number = Column(
        String(50), 
        ForeignKey("project.project_number", ondelete="RESTRICT"), 
        nullable=False,
        index=True,
        comment="项目号"
    )
    work_hours = Column(DECIMAL(5, 2), nullable=False, comment="工时")
    work_content = Column(Text, nullable=False, comment="工作内容")
    remarks = Column(String(500), nullable=True, comment="备注")
    delete_flag = Column(<PERSON><PERSON><PERSON>, nullable=False, default=False, comment="删除标记：0删除，1正常")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        DateTime, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )
    
    # 约束
    __table_args__ = (
        CheckConstraint('work_hours >= 0 AND work_hours <= 8', name='chk_work_hours'),
        UniqueConstraint('work_date', 'employee_id', 'project_number', name='idx_unique_worklog'),
    )
    
    # 关联关系
    employee = relationship("Employee", back_populates="work_logs")
    project = relationship("Project", back_populates="work_logs")
    
    def __repr__(self):
        return f"<WorkLog(id={self.id}, date={self.work_date}, employee_id={self.employee_id}, hours={self.work_hours})>"
