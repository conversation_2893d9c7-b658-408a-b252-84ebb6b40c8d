'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';
import { authApi } from '@/lib/api';
import type { User, LoginRequest } from '@/types';

interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // 获取当前用户信息
  const fetchUser = useCallback(async () => {
    try {
      const token = Cookies.get('access_token');
      if (!token) {
        setIsLoading(false);
        return;
      }

      const userData = await authApi.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      Cookies.remove('access_token');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 登录
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);
      const tokenData = await authApi.login(credentials);
      
      // 保存 token
      Cookies.set('access_token', tokenData.access_token, {
        expires: 1, // 1天过期
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });

      // 获取用户信息
      const userData = await authApi.getCurrentUser();
      setUser(userData);

      toast.success('登录成功');
      router.push('/dashboard');
    } catch (error: any) {
      console.error('登录失败:', error);
      const message = error.response?.data?.detail || '登录失败，请检查用户名和密码';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [router]);

  // 登出
  const logout = useCallback(() => {
    Cookies.remove('access_token');
    setUser(null);
    toast.success('已退出登录');
    router.push('/login');
  }, [router]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    try {
      const userData = await authApi.getCurrentUser();
      setUser(userData);
    } catch (error) {
      console.error('刷新用户信息失败:', error);
    }
  }, []);

  // 初始化时检查登录状态
  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
  };
}
