"use client";

import { useState, useEffect } from "react";
import {
  Cog6ToothIcon,
  UserIcon,
  ShieldCheckIcon,
  CircleStackIcon,
  BellIcon,
  EyeIcon,
  KeyIcon,
  ClockIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { workLog<PERSON>pi, projectApi, employeeApi, departmentApi } from "@/lib/api";
import { formatDate } from "@/lib/utils";

interface SystemStats {
  totalUsers: number;
  totalProjects: number;
  totalDepartments: number;
  totalWorkLogs: number;
  systemUptime: string;
  lastBackup: string;
}

export default function SettingsPage() {
  const { user, canAccessSettings } = useAuth();

  // 权限检查
  if (!canAccessSettings()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">访问受限</h3>
          <p className="mt-1 text-sm text-gray-500">您没有权限访问系统设置</p>
        </div>
      </div>
    );
  }
  const [stats, setStats] = useState<SystemStats>({
    totalUsers: 0,
    totalProjects: 0,
    totalDepartments: 0,
    totalWorkLogs: 0,
    systemUptime: "0天",
    lastBackup: "未知",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  // 导出数据功能
  const handleExportData = async (
    type: "employees" | "projects" | "departments" | "work_logs"
  ) => {
    try {
      setIsExporting(true);
      let data: any[] = [];
      let filename = "";

      switch (type) {
        case "employees":
          data = await employeeApi.getAll({ limit: 1000 });
          filename = "employees.csv";
          break;
        case "projects":
          data = await projectApi.getAll({ limit: 1000 });
          filename = "projects.csv";
          break;
        case "departments":
          data = await departmentApi.getAll();
          filename = "departments.csv";
          break;
        case "work_logs":
          data = await workLogApi.getAll({ limit: 1000 });
          filename = "work_logs.csv";
          break;
      }

      // 转换为CSV格式
      if (data.length > 0) {
        const headers = Object.keys(data[0]).join(",");
        const rows = data.map((item) => Object.values(item).join(","));
        const csv = [headers, ...rows].join("\n");

        // 下载文件
        const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
      }
    } catch (error) {
      console.error("导出数据失败:", error);
    } finally {
      setIsExporting(false);
    }
  };

  useEffect(() => {
    const fetchSystemStats = async () => {
      try {
        setIsLoading(true);
        const [employees, projects, departments, workLogs] = await Promise.all([
          employeeApi.getAll({ limit: 1000 }),
          projectApi.getAll({ limit: 1000 }),
          departmentApi.getAll(),
          workLogApi.getAll({ limit: 1000 }),
        ]);

        setStats({
          totalUsers: employees.length,
          totalProjects: projects.length,
          totalDepartments: departments.length,
          totalWorkLogs: workLogs.length,
          systemUptime: "7天",
          lastBackup: formatDate(new Date()),
        });
      } catch (error) {
        console.error("获取系统统计失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.is_sales || user?.is_admin) {
      fetchSystemStats();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  if (!user?.is_sales && !user?.is_admin) {
    return (
      <div className="min-h-96 flex items-center justify-center">
        <div className="text-center">
          <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">权限不足</h3>
          <p className="mt-1 text-sm text-gray-500">
            只有销售人员或管理员可以访问系统设置
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-gray-300 rounded w-1/4"></div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-64 bg-gray-300 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">系统设置</h1>
        <p className="mt-2 text-sm text-gray-600">管理系统配置和查看系统状态</p>
      </div>

      {/* 系统统计 */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-blue-50">
                  <UserIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总用户数</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalUsers}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-green-50">
                  <DocumentTextIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">项目数量</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalProjects}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-purple-50">
                  <ClockIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">工作日志</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalWorkLogs}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="p-3 rounded-xl bg-yellow-50">
                  <CircleStackIcon className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">部门数量</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalDepartments}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 设置面板 */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* 用户管理 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <UserIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">用户管理</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">
                  用户权限管理
                </p>
                <p className="text-xs text-gray-500">管理用户角色和权限</p>
              </div>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                管理
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">密码策略</p>
                <p className="text-xs text-gray-500">设置密码复杂度要求</p>
              </div>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                配置
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">登录日志</p>
                <p className="text-xs text-gray-500">查看用户登录记录</p>
              </div>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                查看
              </button>
            </div>
          </div>
        </div>

        {/* 系统配置 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-green-100 rounded-lg mr-3">
              <Cog6ToothIcon className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">系统配置</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">系统参数</p>
                <p className="text-xs text-gray-500">配置系统基本参数</p>
              </div>
              <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                设置
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">邮件配置</p>
                <p className="text-xs text-gray-500">配置邮件服务器</p>
              </div>
              <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                配置
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">备份设置</p>
                <p className="text-xs text-gray-500">配置自动备份策略</p>
              </div>
              <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                配置
              </button>
            </div>
          </div>
        </div>

        {/* 安全设置 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-red-100 rounded-lg mr-3">
              <ShieldCheckIcon className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">安全设置</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">访问控制</p>
                <p className="text-xs text-gray-500">配置IP白名单和访问限制</p>
              </div>
              <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                配置
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">会话管理</p>
                <p className="text-xs text-gray-500">管理用户会话和超时</p>
              </div>
              <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                管理
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">审计日志</p>
                <p className="text-xs text-gray-500">查看系统操作记录</p>
              </div>
              <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                查看
              </button>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-purple-100 rounded-lg mr-3">
              <EyeIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">系统状态</h3>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">
                  系统运行时间
                </p>
                <p className="text-xs text-gray-500">{stats.systemUptime}</p>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">
                  最后备份时间
                </p>
                <p className="text-xs text-gray-500">{stats.lastBackup}</p>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                已完成
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-900">数据库状态</p>
                <p className="text-xs text-gray-500">SQLite 数据库</p>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                连接正常
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white shadow-lg rounded-2xl border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <CircleStackIcon className="h-5 w-5 text-gray-600 mr-2" />
            <span className="text-sm font-medium text-gray-700">数据备份</span>
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <KeyIcon className="h-5 w-5 text-gray-600 mr-2" />
            <span className="text-sm font-medium text-gray-700">重置密钥</span>
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <BellIcon className="h-5 w-5 text-gray-600 mr-2" />
            <span className="text-sm font-medium text-gray-700">系统通知</span>
          </button>
          <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <EyeIcon className="h-5 w-5 text-gray-600 mr-2" />
            <span className="text-sm font-medium text-gray-700">系统日志</span>
          </button>
        </div>
      </div>
    </div>
  );
}
