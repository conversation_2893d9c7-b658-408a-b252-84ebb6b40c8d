"""员工模型"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class Employee(Base):
    """员工信息表"""
    __tablename__ = "employee"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    employee_name = Column(String(128), nullable=False, index=True, comment="员工姓名")
    password = Column(String(128), nullable=False, comment="登录密码")
    is_sales = Column(Boolean, nullable=False, default=False, comment="是否为销售")
    is_admin = Column(Boolean, nullable=False, default=False, comment="是否为管理员")
    department_id = Column(
        Integer, 
        ForeignKey("department.id", ondelete="RESTRICT"), 
        nullable=False,
        index=True,
        comment="所属部门ID"
    )
    remarks = Column(String(500), nullable=True, comment="备注")
    delete_flag = Column(<PERSON><PERSON><PERSON>, nullable=False, default=False, comment="删除标记：0删除，1正常")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(
        DateTime, 
        server_default=func.now(), 
        onupdate=func.now(), 
        comment="更新时间"
    )
    
    # 关联关系
    department = relationship("Department", back_populates="employees")
    managed_projects = relationship("Project", back_populates="manager")
    work_logs = relationship("WorkLog", back_populates="employee")
    
    def __repr__(self):
        return f"<Employee(id={self.id}, name='{self.employee_name}')>"
