// API 响应类型
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

// 认证相关类型
export interface LoginRequest {
  employee_name: string;
  password: string;
}

export interface Token {
  access_token: string;
  token_type: string;
}

export interface User {
  id: number;
  employee_name: string;
  is_sales: boolean;
  department_id: number;
  remarks?: string;
  created_at: string;
  updated_at: string;
  department?: Department;
}

// 部门类型
export interface Department {
  id: number;
  department_name: string;
  created_at: string;
  updated_at: string;
}

export interface DepartmentCreate {
  department_name: string;
}

export interface DepartmentUpdate {
  department_name?: string;
}

// 员工类型
export interface Employee {
  id: number;
  employee_name: string;
  is_sales: boolean;
  is_admin: boolean;
  department_id: number;
  remarks?: string;
  created_at: string;
  updated_at: string;
  department?: Department;
}

export interface EmployeeCreate {
  employee_name: string;
  password: string;
  is_sales: boolean;
  is_admin: boolean;
  department_id: number;
  remarks?: string;
}

export interface EmployeeUpdate {
  employee_name?: string;
  password?: string;
  is_sales?: boolean;
  is_admin?: boolean;
  department_id?: number;
  remarks?: string;
}

// 项目类型
export interface Project {
  id: number;
  project_number: string;
  project_type: string;
  customer_abbreviation: string;
  customer_name: string;
  project_name: string;
  manager_id: number;
  remarks?: string;
  created_at: string;
  updated_at: string;
  manager?: Employee;
}

export interface ProjectCreate {
  project_number: string;
  project_type: string;
  customer_abbreviation: string;
  customer_name: string;
  project_name: string;
  manager_id: number;
  remarks?: string;
}

export interface ProjectUpdate {
  project_number?: string;
  project_type?: string;
  customer_abbreviation?: string;
  customer_name?: string;
  project_name?: string;
  manager_id?: number;
  remarks?: string;
}

// 工作日志类型
export interface WorkLog {
  id: number;
  work_date: string;
  employee_id: number;
  project_number: string;
  work_hours: number;
  work_content: string;
  remarks?: string;
  created_at: string;
  updated_at: string;
  employee?: Employee;
  project?: Project;
}

export interface WorkLogCreate {
  work_date: string;
  employee_id: number;
  project_number: string;
  work_hours: number;
  work_content: string;
  remarks?: string;
}

export interface WorkLogUpdate {
  work_date?: string;
  employee_id?: number;
  project_number?: string;
  work_hours?: number;
  work_content?: string;
  remarks?: string;
}

// 统计类型
export interface WorkLogStatistics {
  total_hours: number;
  total_logs: number;
  avg_hours: number;
}

// 表单类型
export interface LoginFormData {
  employee_name: string;
  password: string;
}

export interface WorkLogFormData {
  work_date: string;
  project_number: string;
  work_hours: number;
  work_content: string;
  remarks?: string;
}

// 查询参数类型
export interface WorkLogQueryParams {
  skip?: number;
  limit?: number;
  employee_id?: number;
  project_number?: string;
  start_date?: string;
  end_date?: string;
}
