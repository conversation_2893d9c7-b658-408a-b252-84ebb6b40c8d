{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/employees/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  UserGroupIcon,\n  BuildingOfficeIcon,\n  ShieldCheckIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { employeeApi, departmentApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\nimport CreateEmployeeModal from \"@/components/modals/CreateEmployeeModal\";\nimport type { Employee, Department } from \"@/types\";\n\nexport default function EmployeesPage() {\n  const { user } = useAuth();\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [departments, setDepartments] = useState<Department[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedDepartment, setSelectedDepartment] = useState(\"\");\n  const [selectedRole, setSelectedRole] = useState(\"\");\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [employeesData, departmentsData] = await Promise.all([\n        employeeApi.getAll({ limit: 100 }),\n        departmentApi.getAll(),\n      ]);\n      setEmployees(employeesData);\n      setDepartments(departmentsData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const filteredEmployees = employees.filter((employee) => {\n    const matchesSearch = employee.employee_name\n      .toLowerCase()\n      .includes(searchTerm.toLowerCase());\n    const matchesDepartment =\n      !selectedDepartment ||\n      employee.department_id.toString() === selectedDepartment;\n    const matchesRole =\n      !selectedRole ||\n      (selectedRole === \"sales\" && employee.is_sales) ||\n      (selectedRole === \"regular\" && !employee.is_sales);\n\n    return matchesSearch && matchesDepartment && matchesRole;\n  });\n\n  const salesCount = employees.filter((emp) => emp.is_sales).length;\n  const regularCount = employees.filter((emp) => !emp.is_sales).length;\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">员工管理</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">管理员工信息和权限设置</p>\n        </div>\n        {user?.is_sales && (\n          <button className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\">\n            <PlusIcon className=\"mr-2 h-4 w-4\" />\n            新建员工\n          </button>\n        )}\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-4\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <UserGroupIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总员工数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredEmployees.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <ShieldCheckIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">销售人员</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{salesCount}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <UserIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">普通员工</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {regularCount}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-yellow-50\">\n                  <BuildingOfficeIcon className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">部门数量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {departments.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 筛选器 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              搜索\n            </label>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                placeholder=\"搜索员工姓名...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              部门\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedDepartment}\n              onChange={(e) => setSelectedDepartment(e.target.value)}\n            >\n              <option value=\"\">全部部门</option>\n              {departments.map((department) => (\n                <option key={department.id} value={department.id.toString()}>\n                  {department.department_name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              角色\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedRole}\n              onChange={(e) => setSelectedRole(e.target.value)}\n            >\n              <option value=\"\">全部角色</option>\n              <option value=\"sales\">销售人员</option>\n              <option value=\"regular\">普通员工</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm(\"\");\n                setSelectedDepartment(\"\");\n                setSelectedRole(\"\");\n              }}\n              className=\"w-full px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n            >\n              重置筛选\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 员工列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">员工列表</h3>\n        </div>\n\n        {filteredEmployees.length > 0 ? (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    员工信息\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    部门\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    角色\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    入职时间\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    操作\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredEmployees.map((employee) => (\n                  <tr\n                    key={employee.id}\n                    className=\"hover:bg-gray-50 transition-colors duration-200\"\n                  >\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-white\">\n                              {employee.employee_name.charAt(0)}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {employee.employee_name}\n                          </div>\n                          {employee.remarks && (\n                            <div className=\"text-sm text-gray-500\">\n                              {employee.remarks}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {employee.department?.department_name}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span\n                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          employee.is_sales\n                            ? \"bg-green-100 text-green-800\"\n                            : \"bg-gray-100 text-gray-800\"\n                        }`}\n                      >\n                        {employee.is_sales ? \"销售人员\" : \"普通员工\"}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(employee.created_at)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex items-center space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-800 transition-colors duration-200\">\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        {user?.is_sales && (\n                          <>\n                            <button className=\"text-green-600 hover:text-green-800 transition-colors duration-200\">\n                              <PencilIcon className=\"h-4 w-4\" />\n                            </button>\n                            {employee.id !== user.id && (\n                              <button className=\"text-red-600 hover:text-red-800 transition-colors duration-200\">\n                                <TrashIcon className=\"h-4 w-4\" />\n                              </button>\n                            )}\n                          </>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <UserGroupIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无员工</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedDepartment || selectedRole\n                ? \"没有找到符合条件的员工\"\n                : \"开始添加您的第一个员工吧！\"}\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;;;AAhBA;;;;;;AAoBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,eAAe,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzD,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAChC,oHAAA,CAAA,gBAAa,CAAC,MAAM;aACrB;YACD,aAAa;YACb,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAC;QAC1C,MAAM,gBAAgB,SAAS,aAAa,CACzC,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW;QAClC,MAAM,oBACJ,CAAC,sBACD,SAAS,aAAa,CAAC,QAAQ,OAAO;QACxC,MAAM,cACJ,CAAC,gBACA,iBAAiB,WAAW,SAAS,QAAQ,IAC7C,iBAAiB,aAAa,CAAC,SAAS,QAAQ;QAEnD,OAAO,iBAAiB,qBAAqB;IAC/C;IAEA,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,EAAE,MAAM;IACjE,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,MAAQ,CAAC,IAAI,QAAQ,EAAE,MAAM;IAEpE,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAE3C,CAAA,iBAAA,2BAAA,KAAM,QAAQ,mBACb,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKnD,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;;sDAErD,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;gDAA2B,OAAO,WAAW,EAAE,CAAC,QAAQ;0DACtD,WAAW,eAAe;+CADhB,WAAW,EAAE;;;;;;;;;;;;;;;;;sCAOhC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;sDAE/C,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;oCACP,cAAc;oCACd,sBAAsB;oCACtB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,kBAAkB,MAAM,GAAG,kBAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,kBAAkB,GAAG,CAAC,CAAC;4CA4Bf;6DA3BP,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFACb,SAAS,aAAa,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;0EAIrC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,SAAS,aAAa;;;;;;oEAExB,SAAS,OAAO,kBACf,6LAAC;wEAAI,WAAU;kFACZ,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;;;8DAM3B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;mEACb,uBAAA,SAAS,UAAU,cAAnB,2CAAA,qBAAqB,eAAe;;;;;;;;;;;8DAGzC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,WAAW,AAAC,2EAIX,OAHC,SAAS,QAAQ,GACb,gCACA;kEAGL,SAAS,QAAQ,GAAG,SAAS;;;;;;;;;;;8DAGlC,6LAAC;oDAAG,WAAU;8DACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;8DAEjC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC,gNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;4DAEpB,CAAA,iBAAA,2BAAA,KAAM,QAAQ,mBACb;;kFACE,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC,sNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;oEAEvB,SAAS,EAAE,KAAK,KAAK,EAAE,kBACtB,6LAAC;wEAAO,WAAU;kFAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2CAvD5B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;6CAoE1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,4NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,cAAc,sBAAsB,eACjC,gBACA;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA3UwB;;QACL,0HAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,KAIjB,EAAE,MAAM;QAJS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJiB;IAKhB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,KAI5B,EAAE,MAAM;QAJoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJ4B;IAK3B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/UserIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,KAIjB,EAAE,MAAM;QAJS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJiB;IAKhB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/ShieldCheckIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,KAIxB,EAAE,MAAM;QAJgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJwB;IAKvB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/PencilIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,KAInB,EAAE,MAAM;QAJW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJmB;IAKlB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,KAIhB,EAAE,MAAM;QAJQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJgB;IAKf,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}