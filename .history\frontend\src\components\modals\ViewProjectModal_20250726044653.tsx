"use client";

import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import {
  XMarkIcon,
  FolderIcon,
  UserIcon,
  CalendarIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { formatDate } from "@/lib/utils";
import type { Project } from "@/types";

interface ViewProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

export default function ViewProjectModal({
  isOpen,
  onClose,
  project,
}: ViewProjectModalProps) {
  if (!project) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FolderIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold text-gray-900"
                      >
                        项目详情
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">查看项目详细信息</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <DocumentTextIcon className="h-4 w-4 mr-2" />
                      基本信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          项目号
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.project_number}
                        </p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          项目类型
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.project_type}
                        </p>
                      </div>
                      <div className="sm:col-span-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          项目名称
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.project_name}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 客户信息 */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <UserIcon className="h-4 w-4 mr-2" />
                      客户信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          客户简称
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.customer_abbreviation}
                        </p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          客户全称
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.customer_name}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 管理信息 */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <UserIcon className="h-4 w-4 mr-2" />
                      管理信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          销售经理
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.employee?.employee_name || "未指定"}
                        </p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          所属部门
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {project.employee?.department?.department_name ||
                            "未知"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 时间信息 */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      时间信息
                    </h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          创建时间
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {formatDate(project.created_at)}
                        </p>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">
                          更新时间
                        </label>
                        <p className="mt-1 text-sm text-gray-900">
                          {formatDate(project.updated_at)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 备注信息 */}
                  {project.remarks && (
                    <div className="bg-yellow-50 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                        <DocumentTextIcon className="h-4 w-4 mr-2" />
                        备注信息
                      </h4>
                      <p className="text-sm text-gray-700">{project.remarks}</p>
                    </div>
                  )}

                  {/* 关闭按钮 */}
                  <div className="flex items-center justify-end pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={onClose}
                    >
                      关闭
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
