{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  ClipboardDocumentListIcon,\n  FolderIcon,\n  UserGroupIcon,\n  ClockIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLog<PERSON>pi, projectApi, employeeApi } from \"@/lib/api\";\nimport { formatHours, formatDate, getTodayString } from \"@/lib/utils\";\nimport CreateWorkLogModal from \"@/components/modals/CreateWorkLogModal\";\nimport type { WorkLogStatistics, WorkLog } from \"@/types\";\n\ninterface DashboardStats {\n  totalHours: number;\n  totalLogs: number;\n  totalProjects: number;\n  totalEmployees: number;\n}\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalHours: 0,\n    totalLogs: 0,\n    totalProjects: 0,\n    totalEmployees: 0,\n  });\n  const [recentLogs, setRecentLogs] = useState<WorkLog[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setIsLoading(true);\n\n        // 获取工时统计\n        const workLogStats = await workLogApi.getStatistics({\n          start_date: new Date(\n            new Date().getFullYear(),\n            new Date().getMonth(),\n            1\n          )\n            .toISOString()\n            .split(\"T\")[0],\n          end_date: getTodayString(),\n        });\n\n        // 获取最近的工作日志\n        const logs = await workLogApi.getMy({ limit: 5 });\n\n        // 如果是销售人员，获取更多统计信息\n        let projectCount = 0;\n        let employeeCount = 0;\n\n        if (user?.is_sales) {\n          const [projects, employees] = await Promise.all([\n            projectApi.getAll({ limit: 1000 }),\n            employeeApi.getAll({ limit: 1000 }),\n          ]);\n          projectCount = projects.length;\n          employeeCount = employees.length;\n        }\n\n        setStats({\n          totalHours: workLogStats.total_hours,\n          totalLogs: workLogStats.total_logs,\n          totalProjects: projectCount,\n          totalEmployees: employeeCount,\n        });\n        setRecentLogs(logs);\n      } catch (error) {\n        console.error(\"获取仪表板数据失败:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (user) {\n      fetchDashboardData();\n    }\n  }, [user]);\n\n  const statCards = [\n    {\n      name: \"本月工时\",\n      value: formatHours(stats.totalHours),\n      icon: ClockIcon,\n      gradient: \"from-blue-500 to-blue-600\",\n      bgColor: \"bg-blue-50\",\n      iconColor: \"text-blue-600\",\n      change: \"+12%\",\n      changeType: \"increase\",\n    },\n    {\n      name: \"工作日志\",\n      value: stats.totalLogs.toString(),\n      icon: ClipboardDocumentListIcon,\n      gradient: \"from-green-500 to-green-600\",\n      bgColor: \"bg-green-50\",\n      iconColor: \"text-green-600\",\n      change: \"+8%\",\n      changeType: \"increase\",\n    },\n    ...(user?.is_sales\n      ? [\n          {\n            name: \"项目数量\",\n            value: stats.totalProjects.toString(),\n            icon: FolderIcon,\n            gradient: \"from-yellow-500 to-yellow-600\",\n            bgColor: \"bg-yellow-50\",\n            iconColor: \"text-yellow-600\",\n            change: \"+3%\",\n            changeType: \"increase\",\n          },\n          {\n            name: \"员工数量\",\n            value: stats.totalEmployees.toString(),\n            icon: UserGroupIcon,\n            gradient: \"from-purple-500 to-purple-600\",\n            bgColor: \"bg-purple-50\",\n            iconColor: \"text-purple-600\",\n            change: \"+2%\",\n            changeType: \"increase\",\n          },\n        ]\n      : []),\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <div key={i} className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"h-8 w-8 bg-gray-300 rounded\"></div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <div className=\"h-4 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 欢迎信息 */}\n      <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold\">\n              欢迎回来，{user?.employee_name}！\n            </h1>\n            <p className=\"mt-2 text-blue-100\">\n              今天是 {formatDate(new Date(), \"yyyy年MM月dd日\")}，祝您工作愉快！\n            </p>\n            <div className=\"mt-4 flex items-center space-x-4\">\n              <div className=\"flex items-center\">\n                <div className=\"h-3 w-3 bg-green-400 rounded-full mr-2\"></div>\n                <span className=\"text-sm\">系统运行正常</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"h-3 w-3 bg-yellow-400 rounded-full mr-2\"></div>\n                <span className=\"text-sm\">\n                  {user?.department?.department_name}\n                </span>\n              </div>\n            </div>\n          </div>\n          <div className=\"hidden md:block\">\n            <div className=\"h-24 w-24 bg-white/20 rounded-full flex items-center justify-center\">\n              <svg\n                className=\"h-12 w-12 text-white\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4\">\n        {statCards.map((item) => (\n          <div\n            key={item.name}\n            className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center\">\n                    <div className={`p-3 rounded-xl ${item.bgColor}`}>\n                      <item.icon className={`h-6 w-6 ${item.iconColor}`} />\n                    </div>\n                    <div className=\"ml-4\">\n                      <p className=\"text-sm font-medium text-gray-600 truncate\">\n                        {item.name}\n                      </p>\n                      <p className=\"text-2xl font-bold text-gray-900\">\n                        {item.value}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex items-center\">\n                    <span\n                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                        item.changeType === \"increase\"\n                          ? \"bg-green-100 text-green-800\"\n                          : \"bg-red-100 text-red-800\"\n                      }`}\n                    >\n                      {item.changeType === \"increase\" ? (\n                        <svg\n                          className=\"w-3 h-3 mr-1\"\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path\n                            fillRule=\"evenodd\"\n                            d=\"M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z\"\n                            clipRule=\"evenodd\"\n                          />\n                        </svg>\n                      ) : (\n                        <svg\n                          className=\"w-3 h-3 mr-1\"\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path\n                            fillRule=\"evenodd\"\n                            d=\"M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z\"\n                            clipRule=\"evenodd\"\n                          />\n                        </svg>\n                      )}\n                      {item.change}\n                    </span>\n                    <span className=\"ml-2 text-xs text-gray-500\">vs 上月</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* 最近的工作日志 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100\">\n        <div className=\"px-6 py-5 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-xl font-semibold text-gray-900\">\n              最近的工作日志\n            </h3>\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n              最新 {recentLogs.length} 条\n            </span>\n          </div>\n        </div>\n        <div className=\"p-6\">\n          {recentLogs.length > 0 ? (\n            <div className=\"space-y-4\">\n              {recentLogs.map((log) => (\n                <div\n                  key={log.id}\n                  className=\"bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors duration-200\"\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\">\n                        <ClipboardDocumentListIcon className=\"h-5 w-5 text-white\" />\n                      </div>\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                            {log.project?.project_name}\n                          </span>\n                          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                            {formatHours(log.work_hours)}\n                          </span>\n                        </div>\n                        <span className=\"text-sm text-gray-500\">\n                          {formatDate(log.work_date)}\n                        </span>\n                      </div>\n                      <p className=\"mt-2 text-sm text-gray-700 line-clamp-2\">\n                        {log.work_content}\n                      </p>\n                      {log.remarks && (\n                        <p className=\"mt-1 text-xs text-gray-500\">\n                          备注: {log.remarks}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n              <div className=\"text-center pt-4\">\n                <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\">\n                  查看全部日志\n                  <svg\n                    className=\"ml-2 h-4 w-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M9 5l7 7-7 7\"\n                    />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center\">\n                <ClipboardDocumentListIcon className=\"h-8 w-8 text-gray-400\" />\n              </div>\n              <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                暂无工作日志\n              </h3>\n              <p className=\"mt-2 text-sm text-gray-500\">\n                开始记录您的第一条工作日志吧！\n              </p>\n              <div className=\"mt-6\">\n                <button\n                  onClick={() => setShowCreateModal(true)}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200\"\n                >\n                  <svg\n                    className=\"mr-2 h-4 w-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M12 4v16m8-8H4\"\n                    />\n                  </svg>\n                  创建工作日志\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;;;AAXA;;;;;;AAsBe,SAAS;QA2JL;;IA1JjB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,WAAW;QACX,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;8DAAqB;oBACzB,IAAI;wBACF,aAAa;wBAEb,SAAS;wBACT,MAAM,eAAe,MAAM,oHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;4BAClD,YAAY,IAAI,KACd,IAAI,OAAO,WAAW,IACtB,IAAI,OAAO,QAAQ,IACnB,GAEC,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE;4BAChB,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD;wBACzB;wBAEA,YAAY;wBACZ,MAAM,OAAO,MAAM,oHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;4BAAE,OAAO;wBAAE;wBAE/C,mBAAmB;wBACnB,IAAI,eAAe;wBACnB,IAAI,gBAAgB;wBAEpB,IAAI,iBAAA,2BAAA,KAAM,QAAQ,EAAE;4BAClB,MAAM,CAAC,UAAU,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC9C,oHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oCAAE,OAAO;gCAAK;gCAChC,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oCAAE,OAAO;gCAAK;6BAClC;4BACD,eAAe,SAAS,MAAM;4BAC9B,gBAAgB,UAAU,MAAM;wBAClC;wBAEA,SAAS;4BACP,YAAY,aAAa,WAAW;4BACpC,WAAW,aAAa,UAAU;4BAClC,eAAe;4BACf,gBAAgB;wBAClB;wBACA,cAAc;oBAChB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,cAAc;oBAC9B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,UAAU;YACnC,MAAM,oNAAA,CAAA,YAAS;YACf,UAAU;YACV,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,MAAM,SAAS,CAAC,QAAQ;YAC/B,MAAM,oPAAA,CAAA,4BAAyB;YAC/B,UAAU;YACV,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;QACd;WACI,CAAA,iBAAA,2BAAA,KAAM,QAAQ,IACd;YACE;gBACE,MAAM;gBACN,OAAO,MAAM,aAAa,CAAC,QAAQ;gBACnC,MAAM,sNAAA,CAAA,aAAU;gBAChB,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,YAAY;YACd;YACA;gBACE,MAAM;gBACN,OAAO,MAAM,cAAc,CAAC,QAAQ;gBACpC,MAAM,4NAAA,CAAA,gBAAa;gBACnB,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,YAAY;YACd;SACD,GACD,EAAE;KACP;IAED,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;uBARb;;;;;;;;;;;;;;;IAiBpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAqB;wCAC3B,iBAAA,2BAAA,KAAM,aAAa;wCAAC;;;;;;;8CAE5B,6LAAC;oCAAE,WAAU;;wCAAqB;wCAC3B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,QAAQ;wCAAe;;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,kBAA8B,OAAb,KAAK,OAAO;8DAC5C,cAAA,6LAAC,KAAK,IAAI;wDAAC,WAAW,AAAC,WAAyB,OAAf,KAAK,SAAS;;;;;;;;;;;8DAEjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAIjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAW,AAAC,2EAIX,OAHC,KAAK,UAAU,KAAK,aAChB,gCACA;;wDAGL,KAAK,UAAU,KAAK,2BACnB,6LAAC;4DACC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEACC,UAAS;gEACT,GAAE;gEACF,UAAS;;;;;;;;;;iFAIb,6LAAC;4DACC,WAAU;4DACV,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC;gEACC,UAAS;gEACT,GAAE;gEACF,UAAS;;;;;;;;;;;wDAId,KAAK,MAAM;;;;;;;8DAEd,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtDhD,KAAK,IAAI;;;;;;;;;;0BAgEpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,6LAAC;oCAAK,WAAU;;wCAAgG;wCAC1G,WAAW,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAI5B,6LAAC;wBAAI,WAAU;kCACZ,WAAW,MAAM,GAAG,kBACnB,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC;wCAeF;yDAdb,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;uFACb,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,YAAY;;;;;;sFAE5B,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;8EAG/B,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;sEAG7B,6LAAC;4DAAE,WAAU;sEACV,IAAI,YAAY;;;;;;wDAElB,IAAI,OAAO,kBACV,6LAAC;4DAAE,WAAU;;gEAA6B;gEACnC,IAAI,OAAO;;;;;;;;;;;;;;;;;;;uCA5BnB,IAAI,EAAE;;;;;;8CAmCf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;;4CAAoP;0DAEpQ,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oPAAA,CAAA,4BAAyB;wCAAC,WAAU;;;;;;;;;;;8CAEvC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;4CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GApWwB;;QACL,0HAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}