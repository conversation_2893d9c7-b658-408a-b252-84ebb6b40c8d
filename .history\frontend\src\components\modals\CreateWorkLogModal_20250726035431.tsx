"use client";

import { useState, useEffect, Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import {
  XMarkIcon,
  ClockIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import toast from "react-hot-toast";
import { useAuth } from "@/hooks/useAuth";
import { workLogApi, projectApi } from "@/lib/api";
import { getTodayString } from "@/lib/utils";
import type { Project, WorkLogFormData } from "@/types";

const workLogSchema = z.object({
  work_date: z.string().min(1, "请选择工作日期"),
  project_number: z.string().min(1, "请选择项目"),
  work_hours: z.number().min(0.1, "工时必须大于0").max(8, "工时不能超过8小时"),
  work_content: z
    .string()
    .min(1, "请输入工作内容")
    .max(1000, "工作内容不能超过1000字符"),
  remarks: z.string().max(500, "备注不能超过500字符").optional(),
});

interface CreateWorkLogModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateWorkLogModal({
  isOpen,
  onClose,
  onSuccess,
}: CreateWorkLogModalProps) {
  const { user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<WorkLogFormData>({
    resolver: zodResolver(workLogSchema),
    defaultValues: {
      work_date: getTodayString(),
      project_number: "",
      work_hours: 8,
      work_content: "",
      remarks: "",
    },
  });

  const selectedProjectNumber = watch("project_number");
  const selectedProject = projects.find(
    (p) => p.project_number === selectedProjectNumber
  );

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true);
        const projectsData = await projectApi.getAll({ limit: 100 });
        setProjects(projectsData);
      } catch (error) {
        console.error("获取项目列表失败:", error);
        toast.error("获取项目列表失败");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchProjects();
    }
  }, [isOpen]);

  const onSubmit = async (data: WorkLogFormData) => {
    if (!user) return;

    try {
      setIsSubmitting(true);
      await workLogApi.create({
        ...data,
        employee_id: user.id,
        work_hours: Number(data.work_hours),
      });

      toast.success("工作日志创建成功");
      reset();
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("创建工作日志失败:", error);
      const message = error.response?.data?.detail || "创建工作日志失败";
      toast.error(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold text-gray-900"
                      >
                        新建工作日志
                      </Dialog.Title>
                      <p className="text-sm text-gray-500">
                        记录您的工作内容和时间
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    className="rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* 工作日期 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        工作日期 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        {...register("work_date")}
                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                          errors.work_date
                            ? "border-red-300"
                            : "border-gray-300"
                        }`}
                      />
                      {errors.work_date && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.work_date.message}
                        </p>
                      )}
                    </div>

                    {/* 工作时长 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        工作时长（小时） <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <ClockIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          step="0.5"
                          min="0.1"
                          max="8"
                          {...register("work_hours", { valueAsNumber: true })}
                          className={`block w-full pl-10 pr-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                            errors.work_hours
                              ? "border-red-300"
                              : "border-gray-300"
                          }`}
                          placeholder="8.0"
                        />
                      </div>
                      {errors.work_hours && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.work_hours.message}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 项目选择 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      项目 <span className="text-red-500">*</span>
                    </label>
                    <select
                      {...register("project_number")}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.project_number
                          ? "border-red-300"
                          : "border-gray-300"
                      }`}
                      disabled={isLoading}
                    >
                      <option value="">请选择项目</option>
                      {projects.map((project) => (
                        <option
                          key={project.project_number}
                          value={project.project_number}
                        >
                          {project.project_name} ({project.project_number})
                        </option>
                      ))}
                    </select>
                    {errors.project_number && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.project_number.message}
                      </p>
                    )}
                    {selectedProject && (
                      <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center space-x-2 text-sm">
                          <span className="font-medium text-blue-900">
                            客户:
                          </span>
                          <span className="text-blue-700">
                            {selectedProject.customer_name}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm mt-1">
                          <span className="font-medium text-blue-900">
                            类型:
                          </span>
                          <span className="text-blue-700">
                            {selectedProject.project_type}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 工作内容 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      工作内容 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      {...register("work_content")}
                      rows={4}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.work_content
                          ? "border-red-300"
                          : "border-gray-300"
                      }`}
                      placeholder="请详细描述您今天的工作内容..."
                    />
                    {errors.work_content && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.work_content.message}
                      </p>
                    )}
                  </div>

                  {/* 备注 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      备注
                    </label>
                    <textarea
                      {...register("remarks")}
                      rows={2}
                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.remarks ? "border-red-300" : "border-gray-300"
                      }`}
                      placeholder="可选的备注信息..."
                    />
                    {errors.remarks && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.remarks.message}
                      </p>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button
                      type="button"
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      onClick={handleClose}
                      disabled={isSubmitting}
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || isLoading}
                      className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          创建中...
                        </div>
                      ) : (
                        "创建日志"
                      )}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
