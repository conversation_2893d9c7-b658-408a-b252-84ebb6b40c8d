"""部门管理 API"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..auth import get_current_employee
from ..database import get_db
from ..models.department import Department
from ..models.employee import Employee
from ..schemas.department import Department as DepartmentSchema, DepartmentCreate, DepartmentUpdate

router = APIRouter(prefix="/departments", tags=["部门管理"])


@router.get("/", response_model=List[DepartmentSchema], summary="获取部门列表")
async def get_departments(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取部门列表"""
    departments = db.query(Department).offset(skip).limit(limit).all()
    return departments


@router.get("/{department_id}", response_model=DepartmentSchema, summary="获取部门详情")
async def get_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """获取部门详情"""
    department = db.query(Department).filter(Department.id == department_id).first()
    if department is None:
        raise HTTPException(status_code=404, detail="部门不存在")
    return department


@router.post("/", response_model=DepartmentSchema, summary="创建部门")
async def create_department(
    department: DepartmentCreate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """创建部门"""
    db_department = Department(**department.model_dump())
    db.add(db_department)
    db.commit()
    db.refresh(db_department)
    return db_department


@router.put("/{department_id}", response_model=DepartmentSchema, summary="更新部门")
async def update_department(
    department_id: int,
    department: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """更新部门"""
    db_department = db.query(Department).filter(Department.id == department_id).first()
    if db_department is None:
        raise HTTPException(status_code=404, detail="部门不存在")
    
    update_data = department.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_department, field, value)
    
    db.commit()
    db.refresh(db_department)
    return db_department


@router.delete("/{department_id}", summary="删除部门")
async def delete_department(
    department_id: int,
    db: Session = Depends(get_db),
    current_employee: Employee = Depends(get_current_employee)
):
    """删除部门"""
    db_department = db.query(Department).filter(Department.id == department_id).first()
    if db_department is None:
        raise HTTPException(status_code=404, detail="部门不存在")
    
    # 检查是否有员工关联到此部门
    employee_count = db.query(Employee).filter(Employee.department_id == department_id).count()
    if employee_count > 0:
        raise HTTPException(status_code=400, detail="该部门下还有员工，无法删除")
    
    db.delete(db_department)
    db.commit()
    return {"message": "部门删除成功"}
