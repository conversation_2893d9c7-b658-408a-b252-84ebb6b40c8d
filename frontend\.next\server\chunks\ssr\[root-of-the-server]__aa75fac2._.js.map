{"version": 3, "sources": [], "sections": [{"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from \"axios\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport type {\n  LoginRequest,\n  Token,\n  User,\n  Department,\n  DepartmentCreate,\n  DepartmentUpdate,\n  Employee,\n  EmployeeCreate,\n  EmployeeUpdate,\n  Project,\n  ProjectCreate,\n  ProjectUpdate,\n  WorkLog,\n  WorkLogCreate,\n  WorkLogUpdate,\n  WorkLogStatistics,\n  WorkLogQueryParams,\n} from \"@/types\";\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: `${API_BASE_URL}/api/v1`,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器 - 添加认证 token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = Cookies.get(\"access_token\");\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // 未授权，清除 token 并跳转到登录页\n      Cookies.remove(\"access_token\");\n      window.location.href = \"/login\";\n    } else if (error.response?.data?.detail) {\n      // 显示服务器错误信息\n      toast.error(error.response.data.detail);\n    } else {\n      // 显示通用错误信息\n      toast.error(\"请求失败，请稍后重试\");\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 认证 API\nexport const authApi = {\n  login: async (data: LoginRequest): Promise<Token> => {\n    const response = await apiClient.post<Token>(\"/auth/login\", data);\n    return response.data;\n  },\n\n  getCurrentUser: async (): Promise<User> => {\n    const response = await apiClient.get<User>(\"/employees/me\");\n    return response.data;\n  },\n\n  changePassword: async (data: {\n    current_password: string;\n    new_password: string;\n  }): Promise<{ message: string }> => {\n    const response = await apiClient.post<{ message: string }>(\n      \"/auth/change-password\",\n      data\n    );\n    return response.data;\n  },\n};\n\n// 部门 API\nexport const departmentApi = {\n  getAll: async (): Promise<Department[]> => {\n    const response = await apiClient.get<Department[]>(\"/departments/\");\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Department> => {\n    const response = await apiClient.get<Department>(`/departments/${id}`);\n    return response.data;\n  },\n\n  create: async (data: DepartmentCreate): Promise<Department> => {\n    const response = await apiClient.post<Department>(\"/departments/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: DepartmentUpdate): Promise<Department> => {\n    const response = await apiClient.put<Department>(\n      `/departments/${id}`,\n      data\n    );\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/departments/${id}`);\n  },\n};\n\n// 员工 API\nexport const employeeApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    department_id?: number;\n  }): Promise<Employee[]> => {\n    const response = await apiClient.get<Employee[]>(\"/employees/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Employee> => {\n    const response = await apiClient.get<Employee>(`/employees/${id}`);\n    return response.data;\n  },\n\n  create: async (data: EmployeeCreate): Promise<Employee> => {\n    const response = await apiClient.post<Employee>(\"/employees/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: EmployeeUpdate): Promise<Employee> => {\n    const response = await apiClient.put<Employee>(`/employees/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/employees/${id}`);\n  },\n};\n\n// 项目 API\nexport const projectApi = {\n  getAll: async (params?: {\n    skip?: number;\n    limit?: number;\n    manager_id?: number;\n    project_type?: string;\n  }): Promise<Project[]> => {\n    const response = await apiClient.get<Project[]>(\"/projects/\", { params });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<Project> => {\n    const response = await apiClient.get<Project>(`/projects/${id}`);\n    return response.data;\n  },\n\n  getByNumber: async (projectNumber: string): Promise<Project> => {\n    const response = await apiClient.get<Project>(\n      `/projects/by-number/${projectNumber}`\n    );\n    return response.data;\n  },\n\n  create: async (data: ProjectCreate): Promise<Project> => {\n    const response = await apiClient.post<Project>(\"/projects/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: ProjectUpdate): Promise<Project> => {\n    const response = await apiClient.put<Project>(`/projects/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/projects/${id}`);\n  },\n};\n\n// 工作日志 API\nexport const workLogApi = {\n  getAll: async (params?: WorkLogQueryParams): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/\", { params });\n    return response.data;\n  },\n\n  getMy: async (\n    params?: Omit<WorkLogQueryParams, \"employee_id\">\n  ): Promise<WorkLog[]> => {\n    const response = await apiClient.get<WorkLog[]>(\"/work-logs/my\", {\n      params,\n    });\n    return response.data;\n  },\n\n  getById: async (id: number): Promise<WorkLog> => {\n    const response = await apiClient.get<WorkLog>(`/work-logs/${id}`);\n    return response.data;\n  },\n\n  getStatistics: async (\n    params?: Omit<WorkLogQueryParams, \"skip\" | \"limit\">\n  ): Promise<WorkLogStatistics> => {\n    const response = await apiClient.get<WorkLogStatistics>(\n      \"/work-logs/statistics\",\n      { params }\n    );\n    return response.data;\n  },\n\n  create: async (data: WorkLogCreate): Promise<WorkLog> => {\n    const response = await apiClient.post<WorkLog>(\"/work-logs/\", data);\n    return response.data;\n  },\n\n  update: async (id: number, data: WorkLogUpdate): Promise<WorkLog> => {\n    const response = await apiClient.put<WorkLog>(`/work-logs/${id}`, data);\n    return response.data;\n  },\n\n  delete: async (id: number): Promise<void> => {\n    await apiClient.delete(`/work-logs/${id}`);\n  },\n};\n\nexport default apiClient;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAqBA,WAAW;AACX,MAAM,eAAe,6DAAmC;AAExD,cAAc;AACd,MAAM,YAA2B,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS,GAAG,aAAa,OAAO,CAAC;IACjC,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qBAAqB;AACrB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,uBAAuB;QACvB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;QACvC,YAAY;QACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;IACxC,OAAO;QACL,WAAW;QACX,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;IACd;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,UAAU,IAAI,CAAQ,eAAe;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAO;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB,OAAO;QAIrB,MAAM,WAAW,MAAM,UAAU,IAAI,CACnC,yBACA;QAEF,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ;QACN,MAAM,WAAW,MAAM,UAAU,GAAG,CAAe;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,CAAC,aAAa,EAAE,IAAI;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAa,iBAAiB;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,CAAC,aAAa,EAAE,IAAI,EACpB;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC7C;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,OAAO;QAKb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAa,eAAe;YAAE;QAAO;QACzE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAW,eAAe;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QAMb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,cAAc;YAAE;QAAO;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,CAAC,oBAAoB,EAAE,eAAe;QAExC,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI,EAAE;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC1C;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,eAAe;YAAE;QAAO;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OACL;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAAY,iBAAiB;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,WAAW,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe,OACb;QAEA,MAAM,WAAW,MAAM,UAAU,GAAG,CAClC,yBACA;YAAE;QAAO;QAEX,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,UAAU,IAAI,CAAU,eAAe;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO,IAAY;QACzB,MAAM,WAAW,MAAM,UAAU,GAAG,CAAU,CAAC,WAAW,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,UAAU,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Cookies from \"js-cookie\";\nimport toast from \"react-hot-toast\";\nimport { authApi } from \"@/lib/api\";\nimport type { User, LoginRequest } from \"@/types\";\n\ninterface UseAuthReturn {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (credentials: LoginRequest) => Promise<void>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n  hasPermission: (permission: string) => boolean;\n  canManageEmployees: () => boolean;\n  canManageProjects: () => boolean;\n  canManageDepartments: () => boolean;\n  canAccessSettings: () => boolean;\n  canViewAllLogs: () => boolean;\n}\n\nexport function useAuth(): UseAuthReturn {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  const isAuthenticated = !!user;\n\n  // 获取当前用户信息\n  const fetchUser = useCallback(async () => {\n    try {\n      const token = Cookies.get(\"access_token\");\n      if (!token) {\n        setIsLoading(false);\n        return;\n      }\n\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"获取用户信息失败:\", error);\n      Cookies.remove(\"access_token\");\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // 登录\n  const login = useCallback(\n    async (credentials: LoginRequest) => {\n      try {\n        setIsLoading(true);\n        const tokenData = await authApi.login(credentials);\n\n        // 保存 token\n        Cookies.set(\"access_token\", tokenData.access_token, {\n          expires: 1, // 1天过期\n          secure: process.env.NODE_ENV === \"production\",\n          sameSite: \"strict\",\n        });\n\n        // 获取用户信息\n        const userData = await authApi.getCurrentUser();\n        setUser(userData);\n\n        toast.success(\"登录成功\");\n        router.push(\"/dashboard\");\n      } catch (error: any) {\n        console.error(\"登录失败:\", error);\n        const message =\n          error.response?.data?.detail || \"登录失败，请检查用户名和密码\";\n        toast.error(message);\n        throw error;\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [router]\n  );\n\n  // 登出\n  const logout = useCallback(() => {\n    Cookies.remove(\"access_token\");\n    setUser(null);\n    toast.success(\"已退出登录\");\n    router.push(\"/login\");\n  }, [router]);\n\n  // 刷新用户信息\n  const refreshUser = useCallback(async () => {\n    try {\n      const userData = await authApi.getCurrentUser();\n      setUser(userData);\n    } catch (error) {\n      console.error(\"刷新用户信息失败:\", error);\n    }\n  }, []);\n\n  // 权限检查函数\n  const hasPermission = useCallback(\n    (permission: string) => {\n      if (!user) return false;\n\n      // 管理员（admin）具有所有权限\n      if (user.is_admin) return true;\n\n      switch (permission) {\n        case \"manage_employees\":\n          return user.is_admin; // 只有管理员可以管理员工\n        case \"manage_projects\":\n          return true; // 所有员工都可以管理项目\n        case \"manage_departments\":\n          return user.is_sales || user.is_admin; // 销售人员和管理员可以管理部门\n        case \"access_settings\":\n          return user.is_admin; // 只有管理员可以访问系统设置\n        case \"view_all_logs\":\n          return user.is_admin; // 只有管理员可以查看所有日志\n        default:\n          return false;\n      }\n    },\n    [user]\n  );\n\n  const canManageEmployees = useCallback(\n    () => hasPermission(\"manage_employees\"),\n    [hasPermission]\n  );\n  const canManageProjects = useCallback(\n    () => hasPermission(\"manage_projects\"),\n    [hasPermission]\n  );\n  const canManageDepartments = useCallback(\n    () => hasPermission(\"manage_departments\"),\n    [hasPermission]\n  );\n  const canAccessSettings = useCallback(\n    () => hasPermission(\"access_settings\"),\n    [hasPermission]\n  );\n  const canViewAllLogs = useCallback(\n    () => hasPermission(\"view_all_logs\"),\n    [hasPermission]\n  );\n\n  // 初始化时检查登录状态\n  useEffect(() => {\n    fetchUser();\n  }, [fetchUser]);\n\n  return {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    logout,\n    refreshUser,\n    hasPermission,\n    canManageEmployees,\n    canManageProjects,\n    canManageDepartments,\n    canAccessSettings,\n    canViewAllLogs,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAwBO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC,CAAC;IAE1B,WAAW;IACX,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI;YACF,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAC1B,IAAI,CAAC,OAAO;gBACV,aAAa;gBACb;YACF;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjB,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,KAAK;IACL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtB,OAAO;QACL,IAAI;YACF,aAAa;YACb,MAAM,YAAY,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAEtC,WAAW;YACX,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,YAAY,EAAE;gBAClD,SAAS;gBACT,QAAQ,oDAAyB;gBACjC,UAAU;YACZ;YAEA,SAAS;YACT,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;YAER,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;YAClC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF,GACA;QAAC;KAAO;IAGV,KAAK;IACL,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,QAAQ;QACR,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACd,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc;YAC7C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,IAAI,CAAC,MAAM,OAAO;QAElB,mBAAmB;QACnB,IAAI,KAAK,QAAQ,EAAE,OAAO;QAE1B,OAAQ;YACN,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,cAAc;YACtC,KAAK;gBACH,OAAO,MAAM,cAAc;YAC7B,KAAK;gBACH,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE,iBAAiB;YAC1D,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;YACxC,KAAK;gBACH,OAAO,KAAK,QAAQ,EAAE,gBAAgB;YACxC;gBACE,OAAO;QACX;IACF,GACA;QAAC;KAAK;IAGR,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,IAAM,cAAc,qBACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,IAAM,cAAc,oBACpB;QAAC;KAAc;IAEjB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,IAAM,cAAc,uBACpB;QAAC;KAAc;IAEjB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,IAAM,cAAc,oBACpB;QAAC;KAAc;IAEjB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,IAAM,cAAc,kBACpB;QAAC;KAAc;IAGjB,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport {\n  EyeIcon,\n  EyeSlashIcon,\n  UserIcon,\n  LockClosedIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport type { LoginFormData } from \"@/types\";\nimport toast from \"react-hot-toast\";\n\n// 表单验证模式\nconst loginSchema = z.object({\n  employee_name: z.string().min(1, \"请输入员工姓名\"),\n  password: z.string().min(1, \"请输入密码\"),\n});\n\nexport default function LoginPage() {\n  const [showPassword, setShowPassword] = useState(false);\n  const { login, isLoading, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  // 如果已登录，重定向到仪表板\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push(\"/dashboard\");\n    }\n  }, [isAuthenticated, router]);\n\n  const onSubmit = async (data: LoginFormData) => {\n    try {\n      await login(data);\n    } catch (error: any) {\n      // 确保错误信息显示\n      console.error(\"登录错误:\", error);\n      const message =\n        error.response?.data?.detail ||\n        error.message ||\n        \"登录失败，请检查用户名和密码\";\n      toast.error(message);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      {/* 背景装饰 */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-50\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full opacity-50\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full opacity-30\"></div>\n      </div>\n\n      <div className=\"relative max-w-md w-full space-y-8\">\n        {/* 登录卡片 */}\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8\">\n          {/* Logo 和标题 */}\n          <div className=\"text-center\">\n            <div className=\"mx-auto h-16 w-16 flex items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg\">\n              <svg\n                className=\"h-10 w-10 text-white\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                />\n              </svg>\n            </div>\n            <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\n              工时管理系统\n            </h2>\n            <p className=\"mt-2 text-sm text-gray-600\">\n              请使用您的员工账号登录系统\n            </p>\n          </div>\n\n          {/* 登录表单 */}\n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n            <div className=\"space-y-4\">\n              {/* 用户名输入框 */}\n              <div>\n                <label\n                  htmlFor=\"employee_name\"\n                  className=\"block text-sm font-medium text-gray-700 mb-2\"\n                >\n                  员工姓名\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <UserIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    {...register(\"employee_name\")}\n                    type=\"text\"\n                    autoComplete=\"username\"\n                    className={`block w-full pl-10 pr-3 py-3 border ${\n                      errors.employee_name\n                        ? \"border-red-300 focus:ring-red-500 focus:border-red-500\"\n                        : \"border-gray-300 focus:ring-blue-500 focus:border-blue-500\"\n                    } rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-all duration-200`}\n                    placeholder=\"请输入员工姓名\"\n                  />\n                </div>\n                {errors.employee_name && (\n                  <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                    <svg\n                      className=\"h-4 w-4 mr-1\"\n                      fill=\"currentColor\"\n                      viewBox=\"0 0 20 20\"\n                    >\n                      <path\n                        fillRule=\"evenodd\"\n                        d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\"\n                        clipRule=\"evenodd\"\n                      />\n                    </svg>\n                    {errors.employee_name.message}\n                  </p>\n                )}\n              </div>\n\n              {/* 密码输入框 */}\n              <div>\n                <label\n                  htmlFor=\"password\"\n                  className=\"block text-sm font-medium text-gray-700 mb-2\"\n                >\n                  登录密码\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <LockClosedIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    {...register(\"password\")}\n                    type={showPassword ? \"text\" : \"password\"}\n                    autoComplete=\"current-password\"\n                    className={`block w-full pl-10 pr-12 py-3 border ${\n                      errors.password\n                        ? \"border-red-300 focus:ring-red-500 focus:border-red-500\"\n                        : \"border-gray-300 focus:ring-blue-500 focus:border-blue-500\"\n                    } rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-all duration-200`}\n                    placeholder=\"请输入登录密码\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors duration-200\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n                {errors.password && (\n                  <p className=\"mt-2 text-sm text-red-600 flex items-center\">\n                    <svg\n                      className=\"h-4 w-4 mr-1\"\n                      fill=\"currentColor\"\n                      viewBox=\"0 0 20 20\"\n                    >\n                      <path\n                        fillRule=\"evenodd\"\n                        d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\"\n                        clipRule=\"evenodd\"\n                      />\n                    </svg>\n                    {errors.password.message}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* 登录按钮 */}\n            <div className=\"pt-4\">\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center\">\n                    <svg\n                      className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <circle\n                        className=\"opacity-25\"\n                        cx=\"12\"\n                        cy=\"12\"\n                        r=\"10\"\n                        stroke=\"currentColor\"\n                        strokeWidth=\"4\"\n                      ></circle>\n                      <path\n                        className=\"opacity-75\"\n                        fill=\"currentColor\"\n                        d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                      ></path>\n                    </svg>\n                    登录中...\n                  </div>\n                ) : (\n                  <>\n                    <span>立即登录</span>\n                    <svg\n                      className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M13 7l5 5m0 0l-5 5m5-5H6\"\n                      />\n                    </svg>\n                  </>\n                )}\n              </button>\n            </div>\n\n            {/* 测试账号信息 */}\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200\">\n              <h4 className=\"text-sm font-medium text-blue-900 mb-2\">\n                测试账号\n              </h4>\n              <div className=\"space-y-1 text-xs text-blue-700\">\n                <p>• 张三 / 123456 (技术部员工)</p>\n                <p>• 李四 / 123456 (销售部经理)</p>\n                <p>• 王五 / 123456 (技术部员工)</p>\n                <p>• 赵六 / 123456 (销售部员工)</p>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        {/* 底部信息 */}\n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500\">\n            © 2024 员工工时日志管理系统. 基于 FastAPI + Next.js 构建\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AAEA;AAfA;;;;;;;;;;AAiBA,SAAS;AACT,MAAM,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,eAAe,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,MAAM;QACd,EAAE,OAAO,OAAY;YACnB,WAAW;YACX,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM,UACJ,MAAM,QAAQ,EAAE,MAAM,UACtB,MAAM,OAAO,IACb;YACF,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;kDAIR,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;gCAAK,WAAU;gCAAiB,UAAU,aAAa;;kDACtD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDACC,SAAQ;wDACR,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEACE,GAAG,SAAS,gBAAgB;gEAC7B,MAAK;gEACL,cAAa;gEACb,WAAW,CAAC,oCAAoC,EAC9C,OAAO,aAAa,GAChB,2DACA,4DACL,qIAAqI,CAAC;gEACvI,aAAY;;;;;;;;;;;;oDAGf,OAAO,aAAa,kBACnB,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;gEACC,WAAU;gEACV,MAAK;gEACL,SAAQ;0EAER,cAAA,8OAAC;oEACC,UAAS;oEACT,GAAE;oEACF,UAAS;;;;;;;;;;;4DAGZ,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;0DAMnC,8OAAC;;kEACC,8OAAC;wDACC,SAAQ;wDACR,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,2NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;0EAE5B,8OAAC;gEACE,GAAG,SAAS,WAAW;gEACxB,MAAM,eAAe,SAAS;gEAC9B,cAAa;gEACb,WAAW,CAAC,qCAAqC,EAC/C,OAAO,QAAQ,GACX,2DACA,4DACL,qIAAqI,CAAC;gEACvI,aAAY;;;;;;0EAEd,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,gBAAgB,CAAC;0EAE/B,6BACC,8OAAC,uNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;yFAExB,8OAAC,6MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;;oDAIxB,OAAO,QAAQ,kBACd,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;gEACC,WAAU;gEACV,MAAK;gEACL,SAAQ;0EAER,cAAA,8OAAC;oEACC,UAAS;oEACT,GAAE;oEACF,UAAS;;;;;;;;;;;4DAGZ,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,0BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAU;wDACV,OAAM;wDACN,MAAK;wDACL,SAAQ;;0EAER,8OAAC;gEACC,WAAU;gEACV,IAAG;gEACH,IAAG;gEACH,GAAE;gEACF,QAAO;gEACP,aAAY;;;;;;0EAEd,8OAAC;gEACC,WAAU;gEACV,MAAK;gEACL,GAAE;;;;;;;;;;;;oDAEA;;;;;;qEAIR;;kEACE,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDACC,WAAU;wDACV,MAAK;wDACL,QAAO;wDACP,SAAQ;kEAER,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,aAAa;4DACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;kDASd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;kEACH,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}