{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/departments/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  BuildingOfficeIcon,\n  UserGroupIcon,\n  PencilIcon,\n  TrashIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { departmentApi, employeeApi } from \"@/lib/api\";\nimport { formatDate } from \"@/lib/utils\";\nimport CreateDepartmentModal from \"@/components/modals/CreateDepartmentModal\";\nimport type { Department, Employee } from \"@/types\";\n\nexport default function DepartmentsPage() {\n  const { user } = useAuth();\n  const [departments, setDepartments] = useState<Department[]>([]);\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [departmentsData, employeesData] = await Promise.all([\n        departmentApi.getAll(),\n        employeeApi.getAll({ limit: 100 }),\n      ]);\n      setDepartments(departmentsData);\n      setEmployees(employeesData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  // 计算每个部门的员工数量\n  const getDepartmentEmployeeCount = (departmentId: number) => {\n    return employees.filter((emp) => emp.department_id === departmentId).length;\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">部门管理</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">管理组织架构和部门信息</p>\n        </div>\n        {user?.is_sales && (\n          <button className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\">\n            <PlusIcon className=\"mr-2 h-4 w-4\" />\n            新建部门\n          </button>\n        )}\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-3\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <BuildingOfficeIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总部门数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {departments.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <UserGroupIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总员工数</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {employees.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <UserGroupIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">平均规模</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {departments.length > 0\n                    ? Math.round(employees.length / departments.length)\n                    : 0}\n                  人\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 部门列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">部门列表</h3>\n        </div>\n\n        {departments.length > 0 ? (\n          <div className=\"grid grid-cols-1 gap-6 p-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {departments.map((department) => {\n              const employeeCount = getDepartmentEmployeeCount(department.id);\n              const departmentEmployees = employees.filter(\n                (emp) => emp.department_id === department.id\n              );\n\n              return (\n                <div\n                  key={department.id}\n                  className=\"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 hover:shadow-lg transition-all duration-200 transform hover:-translate-y-1\"\n                >\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"p-2 bg-blue-100 rounded-lg\">\n                        <BuildingOfficeIcon className=\"h-6 w-6 text-blue-600\" />\n                      </div>\n                      <div>\n                        <h4 className=\"text-lg font-semibold text-gray-900\">\n                          {department.department_name}\n                        </h4>\n                        <p className=\"text-sm text-gray-500\">\n                          {employeeCount} 名员工\n                        </p>\n                      </div>\n                    </div>\n                    {user?.is_sales && (\n                      <div className=\"flex items-center space-x-1\">\n                        <button className=\"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200\">\n                          <PencilIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button className=\"p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200\">\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-gray-600\">创建时间</span>\n                      <span className=\"text-gray-900\">\n                        {formatDate(department.created_at)}\n                      </span>\n                    </div>\n\n                    {employeeCount > 0 && (\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700 mb-2\">\n                          部门成员\n                        </p>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {departmentEmployees.slice(0, 3).map((employee) => (\n                            <span\n                              key={employee.id}\n                              className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                            >\n                              {employee.employee_name}\n                            </span>\n                          ))}\n                          {employeeCount > 3 && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600\">\n                              +{employeeCount - 3}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    )}\n\n                    {employeeCount === 0 && (\n                      <div className=\"text-center py-4\">\n                        <UserGroupIcon className=\"mx-auto h-8 w-8 text-gray-400\" />\n                        <p className=\"mt-1 text-sm text-gray-500\">暂无员工</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <BuildingOfficeIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无部门</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              开始创建您的第一个部门吧！\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AAZA;;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,iBAAiB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzD,iHAAA,CAAA,gBAAa,CAAC,MAAM;gBACpB,iHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;aACjC;YACD,eAAe;YACf,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,6BAA6B,CAAC;QAClC,OAAO,UAAU,MAAM,CAAC,CAAC,MAAQ,IAAI,aAAa,KAAK,cAAc,MAAM;IAC7E;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;oBAE3C,MAAM,0BACL,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;;oDACV,YAAY,MAAM,GAAG,IAClB,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG,YAAY,MAAM,IAChD;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,YAAY,MAAM,GAAG,kBACpB,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC;4BAChB,MAAM,gBAAgB,2BAA2B,WAAW,EAAE;4BAC9D,MAAM,sBAAsB,UAAU,MAAM,CAC1C,CAAC,MAAQ,IAAI,aAAa,KAAK,WAAW,EAAE;4BAG9C,qBACE,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,mOAAA,CAAA,qBAAkB;4DAAC,WAAU;;;;;;;;;;;kEAEhC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,WAAW,eAAe;;;;;;0EAE7B,8OAAC;gEAAE,WAAU;;oEACV;oEAAc;;;;;;;;;;;;;;;;;;;4CAIpB,MAAM,0BACL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,mNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,8OAAC;wDAAO,WAAU;kEAChB,cAAA,8OAAC,iNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAM7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,UAAU;;;;;;;;;;;;4CAIpC,gBAAgB,mBACf,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAyC;;;;;;kEAGtD,8OAAC;wDAAI,WAAU;;4DACZ,oBAAoB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBACpC,8OAAC;oEAEC,WAAU;8EAET,SAAS,aAAa;mEAHlB,SAAS,EAAE;;;;;4DAMnB,gBAAgB,mBACf,8OAAC;gEAAK,WAAU;;oEAAgG;oEAC5G,gBAAgB;;;;;;;;;;;;;;;;;;;4CAO3B,kBAAkB,mBACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;+BA/D3C,WAAW,EAAE;;;;;wBAqExB;;;;;6CAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mOAAA,CAAA,qBAAkB;gCAAC,WAAU;;;;;;0CAC9B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/PencilIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/node_modules/%40heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}