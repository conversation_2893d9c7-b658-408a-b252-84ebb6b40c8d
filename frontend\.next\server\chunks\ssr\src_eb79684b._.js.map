{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/CreateWorkLogModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, Fragment } from \"react\";\nimport { Dialog, Transition } from \"@headlessui/react\";\nimport {\n  XMarkIcon,\n  ClockIcon,\n  DocumentTextIcon,\n} from \"@heroicons/react/24/outline\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLogApi, projectApi } from \"@/lib/api\";\nimport { getTodayString } from \"@/lib/utils\";\nimport type { Project, WorkLogFormData } from \"@/types\";\n\nconst workLogSchema = z.object({\n  work_date: z.string().min(1, \"请选择工作日期\"),\n  project_number: z.string().min(1, \"请选择项目\"),\n  work_hours: z.number().min(0.1, \"工时必须大于0\").max(8, \"工时不能超过8小时\"),\n  work_content: z\n    .string()\n    .min(1, \"请输入工作内容\")\n    .max(1000, \"工作内容不能超过1000字符\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface CreateWorkLogModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n}\n\nexport default function CreateWorkLogModal({\n  isOpen,\n  onClose,\n  onSuccess,\n}: CreateWorkLogModalProps) {\n  const { user } = useAuth();\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch,\n  } = useForm<WorkLogFormData>({\n    resolver: zodResolver(workLogSchema),\n    defaultValues: {\n      work_date: getTodayString(),\n      project_number: \"\",\n      work_hours: 8,\n      work_content: \"\",\n      remarks: \"\",\n    },\n  });\n\n  const selectedProjectNumber = watch(\"project_number\");\n  const selectedProject = projects.find(\n    (p) => p.project_number === selectedProjectNumber\n  );\n\n  useEffect(() => {\n    const fetchProjects = async () => {\n      try {\n        setIsLoading(true);\n        const projectsData = await projectApi.getAll({ limit: 100 });\n        setProjects(projectsData);\n      } catch (error) {\n        console.error(\"获取项目列表失败:\", error);\n        toast.error(\"获取项目列表失败\");\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchProjects();\n    }\n  }, [isOpen]);\n\n  const onSubmit = async (data: WorkLogFormData) => {\n    if (!user) return;\n\n    try {\n      setIsSubmitting(true);\n      await workLogApi.create({\n        ...data,\n        employee_id: user.id,\n        work_hours: Number(data.work_hours),\n      });\n\n      toast.success(\"工作日志创建成功\");\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error(\"创建工作日志失败:\", error);\n      const message = error.response?.data?.detail || \"创建工作日志失败\";\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title\n                        as=\"h3\"\n                        className=\"text-lg font-semibold text-gray-900\"\n                      >\n                        新建工作日志\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">\n                        记录您的工作内容和时间\n                      </p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    {/* 工作日期 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作日期 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"date\"\n                        {...register(\"work_date\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.work_date\n                            ? \"border-red-300\"\n                            : \"border-gray-300\"\n                        }`}\n                      />\n                      {errors.work_date && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.work_date.message}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* 工作时长 */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作时长（小时） <span className=\"text-red-500\">*</span>\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                          <ClockIcon className=\"h-5 w-5 text-gray-400\" />\n                        </div>\n                        <input\n                          type=\"number\"\n                          step=\"0.5\"\n                          min=\"0.1\"\n                          max=\"8\"\n                          {...register(\"work_hours\", { valueAsNumber: true })}\n                          className={`block w-full pl-10 pr-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                            errors.work_hours\n                              ? \"border-red-300\"\n                              : \"border-gray-300\"\n                          }`}\n                          placeholder=\"8.0\"\n                        />\n                      </div>\n                      {errors.work_hours && (\n                        <p className=\"mt-1 text-sm text-red-600\">\n                          {errors.work_hours.message}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* 项目选择 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"project_number\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_number\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value=\"\">请选择项目</option>\n                      {projects.map((project) => (\n                        <option\n                          key={project.project_number}\n                          value={project.project_number}\n                        >\n                          {project.project_name} ({project.project_number})\n                        </option>\n                      ))}\n                    </select>\n                    {errors.project_number && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.project_number.message}\n                      </p>\n                    )}\n                    {selectedProject && (\n                      <div className=\"mt-2 p-3 bg-blue-50 rounded-lg\">\n                        <div className=\"flex items-center space-x-2 text-sm\">\n                          <span className=\"font-medium text-blue-900\">\n                            客户:\n                          </span>\n                          <span className=\"text-blue-700\">\n                            {selectedProject.customer_name}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-sm mt-1\">\n                          <span className=\"font-medium text-blue-900\">\n                            类型:\n                          </span>\n                          <span className=\"text-blue-700\">\n                            {selectedProject.project_type}\n                          </span>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* 工作内容 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      工作内容 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <textarea\n                      {...register(\"work_content\")}\n                      rows={4}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.work_content\n                          ? \"border-red-300\"\n                          : \"border-gray-300\"\n                      }`}\n                      placeholder=\"请详细描述您今天的工作内容...\"\n                    />\n                    {errors.work_content && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.work_content.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 备注 */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      备注\n                    </label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={2}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">\n                        {errors.remarks.message}\n                      </p>\n                    )}\n                  </div>\n\n                  {/* 操作按钮 */}\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg\n                            className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <circle\n                              className=\"opacity-25\"\n                              cx=\"12\"\n                              cy=\"12\"\n                              r=\"10\"\n                              stroke=\"currentColor\"\n                              strokeWidth=\"4\"\n                            ></circle>\n                            <path\n                              className=\"opacity-75\"\n                              fill=\"currentColor\"\n                              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            ></path>\n                          </svg>\n                          创建中...\n                        </div>\n                      ) : (\n                        \"创建日志\"\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;AAkBA,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG;IAClD,cAAc,6KAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,MAAM;IACb,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAQe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACe;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD;YACxB,gBAAgB;YAChB,YAAY;YACZ,cAAc;YACd,SAAS;QACX;IACF;IAEA,MAAM,wBAAwB,MAAM;IACpC,MAAM,kBAAkB,SAAS,IAAI,CACnC,CAAC,IAAM,EAAE,cAAc,KAAK;IAG9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,MAAM,eAAe,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAC1D,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,gBAAgB;YAChB,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,EAAE;gBACpB,YAAY,OAAO,KAAK,UAAU;YACpC;YAEA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;;;;;;kEAE9B,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEACX,IAAG;gEACH,WAAU;0EACX;;;;;;0EAGD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAKzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,YAAY;gEACzB,WAAW,CAAC,wIAAwI,EAClJ,OAAO,SAAS,GACZ,mBACA,mBACJ;;;;;;4DAEH,OAAO,SAAS,kBACf,8OAAC;gEAAE,WAAU;0EACV,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kEAM/B,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACrD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAE1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;kFAEvB,8OAAC;wEACC,MAAK;wEACL,MAAK;wEACL,KAAI;wEACJ,KAAI;wEACH,GAAG,SAAS,cAAc;4EAAE,eAAe;wEAAK,EAAE;wEACnD,WAAW,CAAC,8IAA8I,EACxJ,OAAO,UAAU,GACb,mBACA,mBACJ;wEACF,aAAY;;;;;;;;;;;;4DAGf,OAAO,UAAU,kBAChB,8OAAC;gEAAE,WAAU;0EACV,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAOlC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EAC3D,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEpC,8OAAC;wDACE,GAAG,SAAS,iBAAiB;wDAC9B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,cAAc,GACjB,mBACA,mBACJ;wDACF,UAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oEAEC,OAAO,QAAQ,cAAc;;wEAE5B,QAAQ,YAAY;wEAAC;wEAAG,QAAQ,cAAc;wEAAC;;mEAH3C,QAAQ,cAAc;;;;;;;;;;;oDAOhC,OAAO,cAAc,kBACpB,8OAAC;wDAAE,WAAU;kEACV,OAAO,cAAc,CAAC,OAAO;;;;;;oDAGjC,iCACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAG5C,8OAAC;wEAAK,WAAU;kFACb,gBAAgB,aAAa;;;;;;;;;;;;0EAGlC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAG5C,8OAAC;wEAAK,WAAU;kFACb,gBAAgB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAQvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACzD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDACE,GAAG,SAAS,eAAe;wDAC5B,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,YAAY,GACf,mBACA,mBACJ;wDACF,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,8OAAC;wDAAE,WAAU;kEACV,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAMlC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAM7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,MAAK;oEACL,SAAQ;;sFAER,8OAAC;4EACC,WAAU;4EACV,IAAG;4EACH,IAAG;4EACH,GAAE;4EACF,QAAO;4EACP,aAAY;;;;;;sFAEd,8OAAC;4EACC,WAAU;4EACV,MAAK;4EACL,GAAE;;;;;;;;;;;;gEAEA;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/components/modals/EditWorkLogModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, Fragment } from 'react';\nimport { Dialog, Transition } from '@headlessui/react';\nimport { XMarkIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport toast from 'react-hot-toast';\nimport { workLogApi, projectApi } from '@/lib/api';\nimport { formatDate } from '@/lib/utils';\nimport type { Project, WorkLog } from '@/types';\n\nconst workLogSchema = z.object({\n  work_date: z.string().min(1, \"请选择工作日期\"),\n  project_number: z.string().min(1, \"请选择项目\"),\n  work_hours: z.number().min(0.1, \"工时必须大于0\").max(8, \"工时不能超过8小时\"),\n  work_content: z\n    .string()\n    .min(1, \"请输入工作内容\")\n    .max(1000, \"工作内容不能超过1000字符\"),\n  remarks: z.string().max(500, \"备注不能超过500字符\").optional(),\n});\n\ninterface WorkLogFormData {\n  work_date: string;\n  project_number: string;\n  work_hours: number;\n  work_content: string;\n  remarks?: string;\n}\n\ninterface EditWorkLogModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess: () => void;\n  workLog: WorkLog | null;\n}\n\nexport default function EditWorkLogModal({ isOpen, onClose, onSuccess, workLog }: EditWorkLogModalProps) {\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n    setValue,\n  } = useForm<WorkLogFormData>({\n    resolver: zodResolver(workLogSchema),\n  });\n\n  useEffect(() => {\n    const fetchProjects = async () => {\n      try {\n        setIsLoading(true);\n        const projectsData = await projectApi.getAll();\n        setProjects(projectsData);\n      } catch (error) {\n        console.error('获取项目列表失败:', error);\n        toast.error('获取项目列表失败');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchProjects();\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    if (workLog && isOpen) {\n      setValue('work_date', workLog.work_date);\n      setValue('project_number', workLog.project_number);\n      setValue('work_hours', parseFloat(workLog.work_hours.toString()));\n      setValue('work_content', workLog.work_content);\n      setValue('remarks', workLog.remarks || '');\n    }\n  }, [workLog, isOpen, setValue]);\n\n  const onSubmit = async (data: WorkLogFormData) => {\n    if (!workLog) return;\n\n    try {\n      await workLogApi.update(workLog.id, data);\n      toast.success('工作日志更新成功');\n      reset();\n      onSuccess();\n      onClose();\n    } catch (error: any) {\n      console.error('更新工作日志失败:', error);\n      const message = error.response?.data?.detail || '更新工作日志失败';\n      toast.error(message);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      reset();\n      onClose();\n    }\n  };\n\n  return (\n    <Transition appear show={isOpen} as={Fragment}>\n      <Dialog as=\"div\" className=\"relative z-50\" onClose={handleClose}>\n        <Transition.Child\n          as={Fragment}\n          enter=\"ease-out duration-300\"\n          enterFrom=\"opacity-0\"\n          enterTo=\"opacity-100\"\n          leave=\"ease-in duration-200\"\n          leaveFrom=\"opacity-100\"\n          leaveTo=\"opacity-0\"\n        >\n          <div className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm\" />\n        </Transition.Child>\n\n        <div className=\"fixed inset-0 overflow-y-auto\">\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\n            <Transition.Child\n              as={Fragment}\n              enter=\"ease-out duration-300\"\n              enterFrom=\"opacity-0 scale-95\"\n              enterTo=\"opacity-100 scale-100\"\n              leave=\"ease-in duration-200\"\n              leaveFrom=\"opacity-100 scale-100\"\n              leaveTo=\"opacity-0 scale-95\"\n            >\n              <Dialog.Panel className=\"w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\n                      <ClipboardDocumentListIcon className=\"h-6 w-6 text-blue-600\" />\n                    </div>\n                    <div>\n                      <Dialog.Title as=\"h3\" className=\"text-lg font-semibold text-gray-900\">\n                        编辑工作日志\n                      </Dialog.Title>\n                      <p className=\"text-sm text-gray-500\">修改工作日志信息</p>\n                    </div>\n                  </div>\n                  <button\n                    type=\"button\"\n                    className=\"rounded-lg p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200\"\n                    onClick={handleClose}\n                    disabled={isSubmitting}\n                  >\n                    <XMarkIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作日期 <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"date\"\n                        {...register(\"work_date\")}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.work_date ? \"border-red-300\" : \"border-gray-300\"\n                        }`}\n                      />\n                      {errors.work_date && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.work_date.message}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        工作时长（小时） <span className=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"number\"\n                        step=\"0.5\"\n                        min=\"0\"\n                        max=\"8\"\n                        {...register(\"work_hours\", { valueAsNumber: true })}\n                        className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                          errors.work_hours ? \"border-red-300\" : \"border-gray-300\"\n                        }`}\n                        placeholder=\"请输入有效值（0-8）\"\n                      />\n                      {errors.work_hours && (\n                        <p className=\"mt-1 text-sm text-red-600\">{errors.work_hours.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      项目 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      {...register(\"project_number\")}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.project_number ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      disabled={isLoading}\n                    >\n                      <option value=\"\">请选择项目</option>\n                      {projects.map((project) => (\n                        <option key={project.project_number} value={project.project_number}>\n                          {project.project_name} ({project.customer_name})\n                        </option>\n                      ))}\n                    </select>\n                    {errors.project_number && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.project_number.message}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      工作内容 <span className=\"text-red-500\">*</span>\n                    </label>\n                    <textarea\n                      {...register(\"work_content\")}\n                      rows={4}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.work_content ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"请详细描述今天的工作内容...\"\n                    />\n                    {errors.work_content && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.work_content.message}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">备注</label>\n                    <textarea\n                      {...register(\"remarks\")}\n                      rows={2}\n                      className={`block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${\n                        errors.remarks ? \"border-red-300\" : \"border-gray-300\"\n                      }`}\n                      placeholder=\"可选的备注信息...\"\n                    />\n                    {errors.remarks && (\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.remarks.message}</p>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200\">\n                    <button\n                      type=\"button\"\n                      className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\"\n                      onClick={handleClose}\n                      disabled={isSubmitting}\n                    >\n                      取消\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={isSubmitting || isLoading}\n                      className=\"px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\"\n                    >\n                      {isSubmitting ? (\n                        <div className=\"flex items-center\">\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                          </svg>\n                          更新中...\n                        </div>\n                      ) : (\n                        '更新日志'\n                      )}\n                    </button>\n                  </div>\n                </form>\n              </Dialog.Panel>\n            </Transition.Child>\n          </div>\n        </div>\n      </Dialog>\n    </Transition>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAaA,MAAM,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,WAAW,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,gBAAgB,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,YAAY,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG;IAClD,cAAc,6KAAA,CAAA,IAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,MAAM;IACb,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,eAAe,QAAQ;AACtD;AAiBe,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAyB;IACrG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,MAAM,eAAe,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;gBAC5C,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,QAAQ;YACrB,SAAS,aAAa,QAAQ,SAAS;YACvC,SAAS,kBAAkB,QAAQ,cAAc;YACjD,SAAS,cAAc,WAAW,QAAQ,UAAU,CAAC,QAAQ;YAC7D,SAAS,gBAAgB,QAAQ,YAAY;YAC7C,SAAS,WAAW,QAAQ,OAAO,IAAI;QACzC;IACF,GAAG;QAAC;QAAS;QAAQ;KAAS;IAE9B,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YACpC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;YACA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB;YACA;QACF;IACF;IAEA,qBACE,8OAAC,uLAAA,CAAA,aAAU;QAAC,MAAM;QAAC,MAAM;QAAQ,IAAI,qMAAA,CAAA,WAAQ;kBAC3C,cAAA,8OAAC,+KAAA,CAAA,SAAM;YAAC,IAAG;YAAM,WAAU;YAAgB,SAAS;;8BAClD,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;oBACf,IAAI,qMAAA,CAAA,WAAQ;oBACZ,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,OAAM;oBACN,WAAU;oBACV,SAAQ;8BAER,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uLAAA,CAAA,aAAU,CAAC,KAAK;4BACf,IAAI,qMAAA,CAAA,WAAQ;4BACZ,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,OAAM;4BACN,WAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gCAAC,WAAU;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;kEAEvC,8OAAC;;0EACC,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;gEAAC,IAAG;gEAAK,WAAU;0EAAsC;;;;;;0EAGtE,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,8OAAC;wCAAK,UAAU,aAAa;wCAAW,WAAU;;0DAChD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACzD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEtC,8OAAC;gEACC,MAAK;gEACJ,GAAG,SAAS,YAAY;gEACzB,WAAW,CAAC,wIAAwI,EAClJ,OAAO,SAAS,GAAG,mBAAmB,mBACtC;;;;;;4DAEH,OAAO,SAAS,kBACf,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kEAItE,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;kFACrD,8OAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAE1C,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACH,GAAG,SAAS,cAAc;oEAAE,eAAe;gEAAK,EAAE;gEACnD,WAAW,CAAC,wIAAwI,EAClJ,OAAO,UAAU,GAAG,mBAAmB,mBACvC;gEACF,aAAY;;;;;;4DAEb,OAAO,UAAU,kBAChB,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAKzE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EAC3D,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEpC,8OAAC;wDACE,GAAG,SAAS,iBAAiB;wDAC9B,WAAW,CAAC,wIAAwI,EAClJ,OAAO,cAAc,GAAG,mBAAmB,mBAC3C;wDACF,UAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oEAAoC,OAAO,QAAQ,cAAc;;wEAC/D,QAAQ,YAAY;wEAAC;wEAAG,QAAQ,aAAa;wEAAC;;mEADpC,QAAQ,cAAc;;;;;;;;;;;oDAKtC,OAAO,cAAc,kBACpB,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;0DAI3E,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;4DAA+C;0EACzD,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDACE,GAAG,SAAS,eAAe;wDAC5B,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,YAAY,GAAG,mBAAmB,mBACzC;wDACF,aAAY;;;;;;oDAEb,OAAO,YAAY,kBAClB,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0DAIzE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACE,GAAG,SAAS,UAAU;wDACvB,MAAM;wDACN,WAAW,CAAC,wIAAwI,EAClJ,OAAO,OAAO,GAAG,mBAAmB,mBACpC;wDACF,aAAY;;;;;;oDAEb,OAAO,OAAO,kBACb,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,UAAU,gBAAgB;wDAC1B,WAAU;kEAET,6BACC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA6C,MAAK;oEAAO,SAAQ;;sFAC9E,8OAAC;4EAAO,WAAU;4EAAa,IAAG;4EAAK,IAAG;4EAAK,GAAE;4EAAK,QAAO;4EAAe,aAAY;;;;;;sFACxF,8OAAC;4EAAK,WAAU;4EAAa,MAAK;4EAAe,GAAE;;;;;;;;;;;;gEAC/C;;;;;;mEAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-log-system/frontend/src/app/dashboard/work-logs/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  PlusIcon,\n  MagnifyingGlassIcon,\n  CalendarIcon,\n  ClockIcon,\n  DocumentTextIcon,\n} from \"@heroicons/react/24/outline\";\nimport toast from \"react-hot-toast\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { workLogApi, projectApi } from \"@/lib/api\";\nimport { formatHours, formatDate } from \"@/lib/utils\";\nimport CreateWorkLogModal from \"@/components/modals/CreateWorkLogModal\";\nimport EditWorkLogModal from \"@/components/modals/EditWorkLogModal\";\nimport type { WorkLog, Project } from \"@/types\";\n\nexport default function WorkLogsPage() {\n  const { user } = useAuth();\n  const [workLogs, setWorkLogs] = useState<WorkLog[]>([]);\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [selectedProject, setSelectedProject] = useState(\"\");\n  const [dateRange, setDateRange] = useState({\n    start: \"\",\n    end: \"\",\n  });\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedWorkLog, setSelectedWorkLog] = useState<WorkLog | null>(null);\n\n  const fetchData = async () => {\n    try {\n      setIsLoading(true);\n      const [logsData, projectsData] = await Promise.all([\n        // 管理员可以查看所有工作日志，普通用户只能查看自己的\n        user?.is_admin\n          ? workLogApi.getAll({ limit: 100 })\n          : workLogApi.getMy({ limit: 50 }),\n        projectApi.getAll({ limit: 100 }),\n      ]);\n      setWorkLogs(logsData);\n      setProjects(projectsData);\n    } catch (error) {\n      console.error(\"获取数据失败:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const handleEdit = (workLog: WorkLog) => {\n    setSelectedWorkLog(workLog);\n    setShowEditModal(true);\n  };\n\n  const handleDelete = async (workLog: WorkLog) => {\n    if (window.confirm(`确定要删除这条工作日志吗？`)) {\n      try {\n        await workLogApi.delete(workLog.id);\n        toast.success(\"工作日志删除成功\");\n        fetchData();\n      } catch (error: any) {\n        console.error(\"删除工作日志失败:\", error);\n        const message = error.response?.data?.detail || \"删除工作日志失败\";\n        toast.error(message);\n      }\n    }\n  };\n\n  const filteredLogs = workLogs.filter((log) => {\n    const matchesSearch =\n      log.work_content.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      log.project?.project_name\n        ?.toLowerCase()\n        .includes(searchTerm.toLowerCase());\n    const matchesProject =\n      !selectedProject || log.project_number === selectedProject;\n    const matchesDateRange =\n      (!dateRange.start || log.work_date >= dateRange.start) &&\n      (!dateRange.end || log.work_date <= dateRange.end);\n\n    return matchesSearch && matchesProject && matchesDateRange;\n  });\n\n  const totalHours = filteredLogs.reduce(\n    (sum, log) => sum + Number(log.work_hours),\n    0\n  );\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse space-y-6\">\n        <div className=\"h-8 bg-gray-300 rounded w-1/4\"></div>\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"h-24 bg-gray-300 rounded-lg\"></div>\n          ))}\n        </div>\n        <div className=\"h-96 bg-gray-300 rounded-lg\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">我的工作日志</h1>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            记录和管理您的日常工作内容\n          </p>\n        </div>\n        <button\n          onClick={() => setShowCreateModal(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-0.5\"\n        >\n          <PlusIcon className=\"mr-2 h-4 w-4\" />\n          新建日志\n        </button>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-3\">\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-blue-50\">\n                  <ClockIcon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">总工时</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {formatHours(totalHours)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-green-50\">\n                  <DocumentTextIcon className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">日志数量</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredLogs.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow-lg rounded-2xl border border-gray-100\">\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"p-3 rounded-xl bg-purple-50\">\n                  <CalendarIcon className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">平均工时</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {filteredLogs.length > 0\n                    ? formatHours(totalHours / filteredLogs.length)\n                    : \"0小时\"}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 筛选器 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              搜索\n            </label>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                placeholder=\"搜索工作内容或项目...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              项目\n            </label>\n            <select\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={selectedProject}\n              onChange={(e) => setSelectedProject(e.target.value)}\n            >\n              <option value=\"\">全部项目</option>\n              {projects.map((project) => (\n                <option\n                  key={project.project_number}\n                  value={project.project_number}\n                >\n                  {project.project_name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              开始日期\n            </label>\n            <input\n              type=\"date\"\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={dateRange.start}\n              onChange={(e) =>\n                setDateRange((prev) => ({ ...prev, start: e.target.value }))\n              }\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              结束日期\n            </label>\n            <input\n              type=\"date\"\n              className=\"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              value={dateRange.end}\n              onChange={(e) =>\n                setDateRange((prev) => ({ ...prev, end: e.target.value }))\n              }\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* 工作日志列表 */}\n      <div className=\"bg-white shadow-lg rounded-2xl border border-gray-100 overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">工作日志列表</h3>\n        </div>\n\n        {filteredLogs.length > 0 ? (\n          <div className=\"divide-y divide-gray-200\">\n            {filteredLogs.map((log) => (\n              <div\n                key={log.id}\n                className=\"p-6 hover:bg-gray-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3 mb-2\">\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                        {log.project?.project_name || \"未知项目\"}\n                      </span>\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                        {formatHours(log.work_hours)}\n                      </span>\n                      <span className=\"text-sm text-gray-500\">\n                        {formatDate(log.work_date)}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <span className=\"text-xs text-gray-500\">客户:</span>\n                      <span className=\"text-xs font-medium text-gray-700 truncate\">\n                        {log.project?.customer_name\n                          ? log.project.customer_name.length > 20\n                            ? `${log.project.customer_name.substring(0, 20)}...`\n                            : log.project.customer_name\n                          : \"未知客户\"}\n                      </span>\n                      {user?.is_admin && log.employee && (\n                        <>\n                          <span className=\"text-xs text-gray-400\">•</span>\n                          <span className=\"text-xs text-gray-500\">员工:</span>\n                          <span className=\"text-xs font-medium text-gray-700\">\n                            {log.employee.employee_name}\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <p className=\"text-gray-900 mb-2\">{log.work_content}</p>\n                    {log.remarks && (\n                      <p className=\"text-sm text-gray-500\">\n                        备注: {log.remarks}\n                      </p>\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    <button\n                      onClick={() => handleEdit(log)}\n                      className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n                    >\n                      编辑\n                    </button>\n                    <button\n                      onClick={() => handleDelete(log)}\n                      className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n                    >\n                      删除\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n              暂无工作日志\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || selectedProject || dateRange.start || dateRange.end\n                ? \"没有找到符合条件的日志\"\n                : \"开始记录您的第一条工作日志吧！\"}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* 新建工作日志模态框 */}\n      <CreateWorkLogModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSuccess={fetchData}\n      />\n\n      {/* 编辑工作日志模态框 */}\n      <EditWorkLogModal\n        isOpen={showEditModal}\n        onClose={() => setShowEditModal(false)}\n        onSuccess={fetchData}\n        workLog={selectedWorkLog}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,OAAO;QACP,KAAK;IACP;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,UAAU,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjD,4BAA4B;gBAC5B,MAAM,WACF,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI,KAC/B,iHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;oBAAE,OAAO;gBAAG;gBACjC,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;aAChC;YACD,YAAY;YACZ,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,CAAC,aAAa,CAAC,GAAG;YACnC,IAAI;gBACF,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAClC,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,aAAa;gBAC3B,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC;QACpC,MAAM,gBACJ,IAAI,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,IAAI,OAAO,EAAE,cACT,cACD,SAAS,WAAW,WAAW;QACpC,MAAM,iBACJ,CAAC,mBAAmB,IAAI,cAAc,KAAK;QAC7C,MAAM,mBACJ,CAAC,CAAC,UAAU,KAAK,IAAI,IAAI,SAAS,IAAI,UAAU,KAAK,KACrD,CAAC,CAAC,UAAU,GAAG,IAAI,IAAI,SAAS,IAAI,UAAU,GAAG;QAEnD,OAAO,iBAAiB,kBAAkB;IAC5C;IAEA,MAAM,aAAa,aAAa,MAAM,CACpC,CAAC,KAAK,MAAQ,MAAM,OAAO,IAAI,UAAU,GACzC;IAGF,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC;wBACC,SAAS,IAAM,mBAAmB;wBAClC,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,aAAa,MAAM,GAAG,IACnB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,aAAa,aAAa,MAAM,IAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;sDAEjC,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;;sDAElD,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAEC,OAAO,QAAQ,cAAc;0DAE5B,QAAQ,YAAY;+CAHhB,QAAQ,cAAc;;;;;;;;;;;;;;;;;sCASnC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,OAAO,UAAU,KAAK;oCACtB,UAAU,CAAC,IACT,aAAa,CAAC,OAAS,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;;;;;;;;;;;;sCAKhE,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,OAAO,UAAU,GAAG;oCACpB,UAAU,CAAC,IACT,aAAa,CAAC,OAAS,CAAC;gDAAE,GAAG,IAAI;gDAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAQlE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;oBAGrD,aAAa,MAAM,GAAG,kBACrB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,IAAI,OAAO,EAAE,gBAAgB;;;;;;sEAEhC,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,IAAI,UAAU;;;;;;sEAE7B,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,SAAS;;;;;;;;;;;;8DAG7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEACb,IAAI,OAAO,EAAE,gBACV,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,KACjC,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAClD,IAAI,OAAO,CAAC,aAAa,GAC3B;;;;;;wDAEL,MAAM,YAAY,IAAI,QAAQ,kBAC7B;;8EACE,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EACb,IAAI,QAAQ,CAAC,aAAa;;;;;;;;;;;;;;8DAKnC,8OAAC;oDAAE,WAAU;8DAAsB,IAAI,YAAY;;;;;;gDAClD,IAAI,OAAO,kBACV,8OAAC;oDAAE,WAAU;;wDAAwB;wDAC9B,IAAI,OAAO;;;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,WAAW;oDAC1B,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;8DACX;;;;;;;;;;;;;;;;;;+BApDA,IAAI,EAAE;;;;;;;;;6CA6DjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CACV,cAAc,mBAAmB,UAAU,KAAK,IAAI,UAAU,GAAG,GAC9D,gBACA;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC,kJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,WAAW;;;;;;0BAIb,8OAAC,gJAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}