"""部门模式"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict


class DepartmentBase(BaseModel):
    """部门基础模式"""
    department_name: str


class DepartmentCreate(DepartmentBase):
    """创建部门模式"""
    pass


class DepartmentUpdate(BaseModel):
    """更新部门模式"""
    department_name: Optional[str] = None


class Department(DepartmentBase):
    """部门模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: datetime
