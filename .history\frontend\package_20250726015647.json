{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.0", "@tailwindcss/forms": "^0.5.10", "@types/js-cookie": "^3.0.6", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lucide-react": "^0.525.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.4.4", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5"}}