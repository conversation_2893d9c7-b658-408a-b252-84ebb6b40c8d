"""员工模式"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict
from .department import Department


class EmployeeBase(BaseModel):
    """员工基础模式"""
    employee_name: str
    is_sales: bool = False
    is_admin: bool = False
    department_id: int
    remarks: Optional[str] = None


class EmployeeCreate(EmployeeBase):
    """创建员工模式"""
    password: str


class EmployeeUpdate(BaseModel):
    """更新员工模式"""
    employee_name: Optional[str] = None
    password: Optional[str] = None
    is_sales: Optional[bool] = None
    is_admin: Optional[bool] = None
    department_id: Optional[int] = None
    remarks: Optional[str] = None


class Employee(EmployeeBase):
    """员工模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: datetime
    department: Optional[Department] = None


class EmployeeInDB(Employee):
    """数据库中的员工模式（包含密码哈希）"""
    password: str
