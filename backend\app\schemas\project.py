"""项目模式"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, ConfigDict
from .employee import Employee


class ProjectBase(BaseModel):
    """项目基础模式"""
    project_number: str
    project_type: str
    customer_abbreviation: str
    customer_name: str
    project_name: str
    employee_id: int
    remarks: Optional[str] = None


class ProjectCreate(ProjectBase):
    """创建项目模式"""
    pass


class ProjectUpdate(BaseModel):
    """更新项目模式"""
    project_number: Optional[str] = None
    project_type: Optional[str] = None
    customer_abbreviation: Optional[str] = None
    customer_name: Optional[str] = None
    project_name: Optional[str] = None
    employee_id: Optional[int] = None
    remarks: Optional[str] = None


class Project(ProjectBase):
    """项目模式"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime
    updated_at: datetime
    employee: Optional[Employee] = None
